#!/usr/bin/env python3
"""
已完成
数据库初始化启动脚本
"""
import sys
import os
import asyncio

# 确保工作目录和Python路径正确
def setup_environment():
    """设置环境"""
    # 获取当前脚本的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 计算项目根目录（向上2级：scripts -> backend -> root）
    project_root = os.path.dirname(os.path.dirname(current_dir))
    # 计算backend目录
    backend_dir = os.path.join(project_root, "backend")

    # 切换到项目根目录
    os.chdir(project_root)
    print(f"切换工作目录到: {project_root}")

    # 添加backend目录到Python路径
    if backend_dir not in sys.path:
        sys.path.insert(0, backend_dir)
        print(f"添加Python路径: {backend_dir}")

    # 确保data目录存在
    data_dir = os.path.join(project_root, "data")
    if not os.path.exists(data_dir):
        os.makedirs(data_dir, exist_ok=True)
        print(f"创建data目录: {data_dir}")

if __name__ == "__main__":
    print("正在初始化YUE商城客服系统数据库...")

    # 设置环境
    setup_environment()

    # 导入并运行主函数
    from app.db.init_db import main

    # 检查命令行参数
    force_reset = "--force" in sys.argv or "--reset" in sys.argv
    if force_reset:
        print("强制重置模式：将删除现有数据库")

    asyncio.run(main(force_reset))
    print("数据库初始化完成！")


