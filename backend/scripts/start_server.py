#!/usr/bin/env python3
"""
服务器启动脚本
使用配置文件中的设置启动FastAPI服务器
"""
import sys
import os
import uvicorn

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings


def main():
    """启动服务器"""
    print("🚀 启动YUE商城智能客服系统")
    print("=" * 50)
    
    # 显示配置信息
    print("📋 服务器配置:")
    print(f"  项目名称: {settings.PROJECT_NAME}")
    print(f"  版本: {settings.VERSION}")
    print(f"  主机: {settings.HOST}")
    print(f"  端口: {settings.PORT}")
    print(f"  调试模式: {settings.DEBUG}")
    print(f"  日志级别: {settings.LOG_LEVEL}")
    
    print("\n🌐 CORS配置:")
    for origin in settings.BACKEND_CORS_ORIGINS:
        print(f"  - {origin}")
    
    print("\n🤖 AI配置:")
    print(f"  模型: {settings.LLM_MODEL}")
    print(f"  API基础URL: {settings.OPENAI_API_BASE or '默认'}")
    
    print("\n🗄️ 数据库:")
    print(f"  数据库URL: {settings.DATABASE_URL}")
    
    print("\n" + "=" * 50)
    print(f"🌍 服务器将在 http://{settings.HOST}:{settings.PORT} 启动")
    print(f"📖 API文档: http://localhost:{settings.PORT}/docs")
    print("=" * 50)
    
    # 启动服务器
    try:
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")


if __name__ == "__main__":
    main()
