"""
配置管理模块
"""
import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import field_validator, ConfigDict


class Settings(BaseSettings):
    """应用配置"""
    
    # 基础配置
    PROJECT_NAME: str = "Yue智能体平台"
    VERSION: str = "0.1.0"
    API_V1_STR: str = "/api/v1"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False
    
    # CORS配置
    BACKEND_CORS_ORIGINS: str = "http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080,http://localhost:5173"

    def get_cors_origins(self) -> List[str]:
        """获取CORS源列表"""
        if isinstance(self.BACKEND_CORS_ORIGINS, str):
            return [origin.strip() for origin in self.BACKEND_CORS_ORIGINS.split(",") if origin.strip()]
        return self.BACKEND_CORS_ORIGINS
    
    # 数据库配置
    DATABASE_URL: str = "sqlite+aiosqlite:///./yue_platform.db"

    # 保留MySQL配置以备后用
    MYSQL_HOST: str = "localhost"
    MYSQL_PORT: int = 3306
    MYSQL_USER: str = "root"
    MYSQL_PASSWORD: str = ""
    MYSQL_DATABASE: str = "yue_platform"
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: Optional[str] = None
    REDIS_DB: int = 0
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # OpenAI配置
    OPENAI_API_KEY: str = ""
    OPENAI_API_BASE: Optional[str] = None
    LLM_MODEL: str = "gpt-3.5-turbo"
    
    # VLLM配置
    VLLM_API_URL: Optional[str] = None
    VLLM_API_KEY: str = ""
    VLLM_API_MODEL: str = "llama2"
    
    # 文件存储配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = ""
    MINIO_SECRET_KEY: str = ""
    MINIO_BUCKET_NAME: str = "yue-platform"
    MINIO_SECURE: bool = False
    
    # 向量数据库配置
    MILVUS_HOST: str = "localhost"
    MILVUS_PORT: int = 19530
    MILVUS_USER: Optional[str] = None
    MILVUS_PASSWORD: Optional[str] = None
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # 客服配置
    CUSTOMER_SERVICE_NAME: str = "小yue"
    MAX_CONVERSATION_HISTORY: int = 50
    HUMAN_HANDOVER_KEYWORDS: List[str] = ["转人工", "人工客服", "找客服", "投诉"]
    
    model_config = ConfigDict(case_sensitive=True)


# 创建全局配置实例
def get_settings():
    """获取配置实例，支持多种配置文件路径"""
    # 尝试不同的配置文件路径
    possible_env_files = [
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", ".env"),
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), ".env"),
        ".env",
        "config/.env"
    ]

    for env_file in possible_env_files:
        if os.path.exists(env_file):
            try:
                return Settings(_env_file=env_file)
            except Exception as e:
                print(f"Warning: Failed to load {env_file}: {e}")
                continue

    # 如果没有找到配置文件或加载失败，使用默认配置
    print("Using default configuration")
    return Settings()

settings = get_settings()
