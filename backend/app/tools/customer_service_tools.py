"""
YUE商城智能客服工具封装

本模块将客服服务方法封装成可供AI模型调用的标准化工具，支持六大核心场景：

📋 核心场景：
    1. 售前咨询 - 产品信息查询
    2. 售前咨询 - 活动与优惠查询
    3. 订单追踪与管理
    4. 售后服务 - 退换货申请
    5. 投诉与建议处理
    6. 人工客服服务转接

🎯 设计原则：
    • 功能明确：每个工具函数职责单一，便于AI理解和选择
    • 统一接口：使用标准化的API调用接口和响应格式
    • 详细文档：包含清晰的功能描述、参数说明和返回值定义
    • 严格类型：使用完整的Python类型注解
    • 错误处理：完善的参数验证和异常处理机制
    • 日志记录：结构化的操作日志记录

📝 作者: YUE AI Platform Team
📅 版本: 2.0.0
🔄 更新时间: 2024-12-19
"""

# ================================================================================================
# 导入依赖
# ================================================================================================

from typing import List, Dict, Any, Optional, Union, Literal, TypedDict
from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.customer_service import CustomerServiceService

import json
import logging
from datetime import datetime
from enum import Enum


# ================================================================================================
# 全局配置
# ================================================================================================

logger = logging.getLogger(__name__)

# ================================================================================================
# 类型定义
# ================================================================================================

class ToolResponse(TypedDict):
    """工具响应标准格式

    定义所有工具方法的统一响应结构，确保返回格式的一致性。
    """
    success: bool                           # 操作是否成功
    data: Optional[Dict[str, Any]]         # 响应数据
    error: Optional[str]                   # 错误信息
    message: Optional[str]                 # 提示信息
    timestamp: str                         # 时间戳


class FeedbackType(str, Enum):
    """反馈类型枚举

    定义用户反馈的标准分类，用于统一处理不同类型的用户意见。
    """
    COMPLAINT = "complaint"                # 投诉
    SUGGESTION = "suggestion"              # 建议
    PRAISE = "praise"                      # 表扬


class RefundReason(str, Enum):
    """退款原因枚举

    定义退款申请的标准原因分类，便于统计分析和处理流程优化。
    """
    QUALITY_ISSUE = "quality_issue"        # 质量问题
    SIZE_MISMATCH = "size_mismatch"        # 尺寸不符
    NOT_AS_DESCRIBED = "not_as_described"  # 与描述不符
    DAMAGED_SHIPPING = "damaged_shipping"  # 运输损坏
    CHANGED_MIND = "changed_mind"          # 改变主意
    OTHER = "other"                        # 其他原因


class ComplaintCategory(str, Enum):
    """投诉分类枚举

    定义投诉的标准分类，便于客服部门分类处理和问题追踪。
    """
    PRODUCT_QUALITY = "product_quality"    # 产品质量
    SERVICE_ATTITUDE = "service_attitude"  # 服务态度
    DELIVERY_ISSUE = "delivery_issue"      # 配送问题
    PAYMENT_ISSUE = "payment_issue"        # 支付问题
    REFUND_ISSUE = "refund_issue"          # 退款问题
    OTHER = "other"                        # 其他问题

# ================================================================================================
# 输入模型定义
# ================================================================================================

class ProductSearchInput(BaseModel):
    """产品搜索输入参数模型

    🔍 功能：用于搜索产品信息，支持多种筛选条件组合查询
    📝 场景：用户询问产品信息、比较产品、查找特定商品等
    """
    query: Optional[str] = Field(
        None,
        description="搜索关键词，支持产品名称、描述等模糊匹配",
        min_length=1,
        max_length=100
    )
    category_id: Optional[int] = Field(
        None,
        description="产品分类ID，用于按分类筛选",
        gt=0
    )
    min_price: Optional[float] = Field(
        None,
        description="最低价格筛选条件",
        ge=0
    )
    max_price: Optional[float] = Field(
        None,
        description="最高价格筛选条件",
        ge=0
    )
    in_stock: bool = Field(
        True,
        description="是否只显示有库存商品"
    )
    limit: int = Field(
        10,
        description="返回结果数量限制",
        ge=1,
        le=50
    )

    @validator('max_price')
    def validate_price_range(cls, v, values):
        """验证价格范围的合理性"""
        if v is not None and 'min_price' in values and values['min_price'] is not None:
            if v < values['min_price']:
                raise ValueError('最高价格不能小于最低价格')
        return v


class OrderQueryInput(BaseModel):
    """订单查询输入参数模型

    🔍 功能：根据订单号查询订单详细信息
    📝 场景：用户查询订单状态、订单详情、物流进度等
    """
    order_number: str = Field(
        ...,
        description="订单号，用于查询订单详细信息",
        min_length=1,
        max_length=50
    )

    @validator('order_number')
    def validate_order_number(cls, v):
        """验证并清理订单号"""
        if not v or not v.strip():
            raise ValueError('订单号不能为空')
        return v.strip()


class CouponValidationInput(BaseModel):
    """优惠券验证输入参数模型

    🔍 功能：验证优惠券代码的有效性和使用条件
    📝 场景：用户使用优惠券前的验证、了解优惠券使用规则等
    """
    code: str = Field(
        ...,
        description="优惠券代码",
        min_length=1,
        max_length=50
    )
    user_id: Optional[int] = Field(
        None,
        description="用户ID，用于验证用户权限",
        gt=0
    )
    order_amount: Optional[float] = Field(
        None,
        description="订单金额，用于计算优惠",
        ge=0
    )

    @validator('code')
    def validate_code(cls, v):
        """验证并标准化优惠券代码"""
        if not v or not v.strip():
            raise ValueError('优惠券代码不能为空')
        return v.strip().upper()


class RefundRequestInput(BaseModel):
    """退款申请输入参数模型

    🔍 功能：提交新的退款申请，包括退款原因、金额等信息
    📝 场景：用户申请退款、处理商品质量问题等
    """
    order_id: int = Field(
        ...,
        description="订单ID",
        gt=0
    )
    user_id: int = Field(
        ...,
        description="用户ID",
        gt=0
    )
    reason: RefundReason = Field(
        ...,
        description="退款原因，必须从预定义选项中选择"
    )
    amount: Optional[float] = Field(
        None,
        description="退款金额，不填则全额退款",
        gt=0
    )
    description: Optional[str] = Field(
        None,
        description="详细说明",
        max_length=500
    )


class ComplaintInput(BaseModel):
    """投诉输入参数模型

    🔍 功能：提交新的用户投诉，包括投诉分类、主题、详细描述等
    📝 场景：用户对产品质量、服务态度、配送问题等进行投诉
    """
    user_id: int = Field(
        ...,
        description="用户ID",
        gt=0
    )
    category: ComplaintCategory = Field(
        ...,
        description="投诉分类，必须从预定义选项中选择"
    )
    subject: str = Field(
        ...,
        description="投诉主题",
        min_length=1,
        max_length=100
    )
    description: str = Field(
        ...,
        description="投诉详细描述",
        min_length=10,
        max_length=1000
    )
    order_id: Optional[int] = Field(
        None,
        description="关联订单ID",
        gt=0
    )

    @validator('subject', 'description')
    def validate_text_fields(cls, v):
        """验证并清理文本字段"""
        if not v or not v.strip():
            raise ValueError('文本字段不能为空')
        return v.strip()


class HumanAgentRequestInput(BaseModel):
    """人工客服请求输入参数模型

    🔍 功能：将用户转接到人工客服，处理复杂问题和特殊需求
    📝 场景：AI无法处理的复杂问题、特殊需求等
    """
    user_id: int = Field(
        ...,
        description="用户ID",
        gt=0
    )
    conversation_id: str = Field(
        ...,
        description="对话ID，用于标识会话",
        min_length=1,
        max_length=100
    )
    reason: Optional[str] = Field(
        None,
        description="请求人工客服的原因",
        max_length=200
    )

    @validator('conversation_id')
    def validate_conversation_id(cls, v):
        """验证并清理对话ID"""
        if not v or not v.strip():
            raise ValueError('对话ID不能为空')
        return v.strip()


# ================================================================================================
# 工具类定义
# ================================================================================================

class CustomerServiceTools:
    """YUE商城智能客服工具类

    🎯 核心功能：
        提供标准化的客服工具接口，支持AI模型调用六大核心场景。

    🔧 设计模式：
        • 严格的参数验证
        • 统一的响应格式
        • 完善的错误处理
        • 详细的日志记录

    📋 支持场景：
        1. 售前咨询 - 产品信息查询
        2. 售前咨询 - 活动与优惠查询
        3. 订单追踪与管理
        4. 售后服务 - 退换货申请
        5. 投诉与建议处理
        6. 人工客服服务转接
    """

    def __init__(self, db: AsyncSession):
        """初始化客服工具类

        Args:
            db: 异步数据库会话，用于访问客服服务
        """
        self.service = CustomerServiceService(db)
        self.db = db

    # ============================================================================================
    # 私有辅助方法
    # ============================================================================================

    def _create_response(
        self,
        success: bool,
        data: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None,
        message: Optional[str] = None
    ) -> str:
        """创建标准化响应格式

        确保所有工具方法返回统一的JSON响应格式，便于AI模型解析和处理。

        Args:
            success: 操作是否成功
            data: 响应数据，包含具体的业务数据
            error: 错误信息，操作失败时的详细说明
            message: 提示信息，给用户的友好提示

        Returns:
            JSON格式的响应字符串，符合ToolResponse类型定义
        """
        response: ToolResponse = {
            "success": success,
            "data": data,
            "error": error,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        return json.dumps(response, ensure_ascii=False, default=str)

    def _log_tool_call(
        self,
        tool_name: str,
        params: Dict[str, Any],
        success: bool,
        error: Optional[str] = None
    ) -> None:
        """记录工具调用日志

        为所有工具调用提供结构化的日志记录，便于监控、调试和性能分析。

        Args:
            tool_name: 工具名称，用于标识具体调用的工具
            params: 调用参数，记录输入数据用于调试
            success: 是否成功，标识操作结果
            error: 错误信息，失败时的详细错误描述
        """
        log_data = {
            "tool": tool_name,
            "params": params,
            "success": success,
            "timestamp": datetime.now().isoformat()
        }

        if success:
            logger.info(f"✅ 工具调用成功: {tool_name}", extra=log_data)
        else:
            logger.error(f"❌ 工具调用失败: {tool_name} - {error}", extra=log_data)

    async def _safe_execute(
        self,
        tool_name: str,
        operation,
        params: Dict[str, Any]
    ) -> str:
        """安全执行工具操作的通用方法

        提供统一的异常处理和响应格式化，确保所有工具方法的行为一致性。

        Args:
            tool_name: 工具名称，用于日志记录和错误追踪
            operation: 要执行的异步操作函数
            params: 操作参数，用于日志记录

        Returns:
            标准化的JSON响应字符串

        Raises:
            无直接异常抛出，所有异常都被捕获并转换为错误响应
        """
        try:
            # 执行业务操作
            result = await operation()
            self._log_tool_call(tool_name, params, True)

            # 根据结果类型格式化响应
            if isinstance(result, dict):
                return self._create_response(True, data=result)
            elif isinstance(result, list):
                return self._create_response(
                    True,
                    data={"items": result, "count": len(result)}
                )
            else:
                return self._create_response(True, data={"result": result})

        except ValueError as e:
            # 参数验证错误 - 用户输入问题
            error_msg = f"参数验证失败: {str(e)}"
            self._log_tool_call(tool_name, params, False, error_msg)
            return self._create_response(False, error=error_msg)

        except Exception as e:
            # 其他系统错误 - 服务器内部问题
            error_msg = f"系统错误: {str(e)}"
            self._log_tool_call(tool_name, params, False, error_msg)
            return self._create_response(False, error=error_msg)
    
    # ============================================================================================
    # 场景1: 售前咨询 - 产品信息查询
    # ============================================================================================

    async def search_products(self, input_data: ProductSearchInput) -> str:
        """🔍 搜索产品信息

        支持多种筛选条件的产品搜索，包括关键词、分类、价格范围等。

        📝 适用场景：
            • 用户询问产品信息
            • 比较不同产品
            • 查找特定商品
            • 按价格范围筛选
            • 按分类浏览商品

        Args:
            input_data: 产品搜索参数，包含搜索关键词、分类、价格范围等筛选条件

        Returns:
            JSON格式的标准化响应，包含产品列表和搜索统计信息

        Example:
            >>> tools = CustomerServiceTools(db)
            >>> search_input = ProductSearchInput(query="iPhone", min_price=1000)
            >>> result = await tools.search_products(search_input)
        """
        params = input_data.dict()

        async def operation():
            products = await self.service.search_products(
                query=input_data.query,
                category_id=input_data.category_id,
                min_price=input_data.min_price,
                max_price=input_data.max_price,
                in_stock=input_data.in_stock,
                limit=input_data.limit
            )
            return {
                "products": products,
                "search_params": params,
                "total_found": len(products)
            }

        return await self._safe_execute("search_products", operation, params)

    async def get_product_details(self, product_id: int) -> str:
        """📋 获取产品详细信息

        根据产品ID获取完整的产品信息，包括规格、价格、库存、评价等。

        📝 适用场景：
            • 用户询问特定产品详情
            • 产品参数对比
            • 查看产品规格
            • 了解产品评价

        Args:
            product_id: 产品ID，必须为正整数

        Returns:
            JSON格式的标准化响应，包含完整的产品详细信息

        Raises:
            ValueError: 当product_id不是正整数时

        Example:
            >>> result = await tools.get_product_details(123)
        """
        if not isinstance(product_id, int) or product_id <= 0:
            return self._create_response(False, error="产品ID必须是正整数")

        params = {"product_id": product_id}

        async def operation():
            product = await self.service.get_product_details(product_id)
            if not product:
                raise ValueError("产品不存在或已下架")
            return {"product": product}

        return await self._safe_execute("get_product_details", operation, params)

    async def check_product_stock(self, product_id: int) -> str:
        """📦 检查产品库存状态

        查询指定产品的实时库存信息，包括可用数量、预留数量等。

        📝 适用场景：
            • 用户询问商品是否有货
            • 查询库存数量
            • 确认商品可购买性
            • 库存预警提醒

        Args:
            product_id: 产品ID，必须为正整数

        Returns:
            JSON格式的标准化响应，包含库存状态和数量信息

        Raises:
            ValueError: 当product_id不是正整数时

        Example:
            >>> result = await tools.check_product_stock(123)
        """
        if not isinstance(product_id, int) or product_id <= 0:
            return self._create_response(False, error="产品ID必须是正整数")

        params = {"product_id": product_id}

        async def operation():
            stock_info = await self.service.check_product_stock(product_id)
            return {"stock": stock_info}

        return await self._safe_execute("check_product_stock", operation, params)
    
    # ============================================================================================
    # 场景2: 售前咨询 - 活动与优惠查询
    # ============================================================================================

    async def get_active_promotions(self) -> str:
        """🎉 获取当前活跃的促销活动

        查询当前正在进行的所有促销活动，包括折扣、满减、买赠等。

        📝 适用场景：
            • 用户询问当前优惠活动
            • 查看促销信息
            • 了解折扣详情
            • 活动时间查询

        Returns:
            JSON格式的标准化响应，包含活跃促销活动列表

        Example:
            >>> result = await tools.get_active_promotions()
        """
        params = {}

        async def operation():
            promotions = await self.service.get_active_promotions()
            return {
                "promotions": promotions,
                "total_count": len(promotions),
                "query_time": datetime.now().isoformat()
            }

        return await self._safe_execute("get_active_promotions", operation, params)

    async def get_user_coupons(self, user_id: int, available_only: bool = True) -> str:
        """获取用户优惠券列表

        查询指定用户的优惠券，可选择只显示可用的优惠券。
        适用于用户查询自己的优惠券、了解可用优惠等场景。

        Args:
            user_id: 用户ID，必须为正整数
            available_only: 是否只返回可用优惠券，默认为True

        Returns:
            JSON格式的标准化响应，包含用户优惠券列表

        Raises:
            ValueError: 当user_id不是正整数时

        Example:
            >>> result = await tools.get_user_coupons(123, available_only=True)
        """
        if not isinstance(user_id, int) or user_id <= 0:
            return self._create_response(False, error="用户ID必须是正整数")

        params = {"user_id": user_id, "available_only": available_only}

        async def operation():
            coupons = await self.service.get_user_coupons(user_id, available_only)
            return {
                "coupons": coupons,
                "total_count": len(coupons),
                "available_only": available_only
            }

        return await self._safe_execute("get_user_coupons", operation, params)

    async def validate_coupon(self, input_data: CouponValidationInput) -> str:
        """验证优惠券有效性

        验证指定优惠券代码的有效性，包括是否存在、是否过期、使用条件等。
        适用于用户使用优惠券前的验证、了解优惠券使用规则等场景。

        Args:
            input_data: 优惠券验证参数，包含优惠券代码、用户ID、订单金额等

        Returns:
            JSON格式的标准化响应，包含验证结果和优惠券详情

        Example:
            >>> result = await tools.validate_coupon(CouponValidationInput(code="SAVE20", user_id=123))
        """
        params = input_data.dict()

        async def operation():
            result = await self.service.validate_coupon(
                code=input_data.code,
                user_id=input_data.user_id,
                order_amount=input_data.order_amount
            )
            return {"validation": result}

        return await self._safe_execute("validate_coupon", operation, params)

    async def get_membership_benefits(self, user_id: int) -> str:
        """获取用户会员权益信息

        查询指定用户的会员等级和对应权益，包括折扣、积分倍率、专属优惠等。
        适用于用户询问会员权益、了解会员等级等场景。

        Args:
            user_id: 用户ID，必须为正整数

        Returns:
            JSON格式的标准化响应，包含会员等级和权益详情

        Raises:
            ValueError: 当user_id不是正整数时

        Example:
            >>> result = await tools.get_membership_benefits(123)
        """
        if not isinstance(user_id, int) or user_id <= 0:
            return self._create_response(False, error="用户ID必须是正整数")

        params = {"user_id": user_id}

        async def operation():
            benefits = await self.service.get_membership_benefits(user_id)
            return {"membership": benefits}

        return await self._safe_execute("get_membership_benefits", operation, params)
    
    # ==================== 场景3: 订单追踪与管理 ====================

    async def get_order_by_number(self, input_data: OrderQueryInput) -> str:
        """根据订单号获取订单详细信息

        通过订单号查询完整的订单信息，包括商品详情、支付状态、物流信息等。
        适用于用户查询订单状态、订单详情、物流进度等场景。

        Args:
            input_data: 订单查询参数，包含订单号

        Returns:
            JSON格式的标准化响应，包含完整的订单信息

        Example:
            >>> result = await tools.get_order_by_number(OrderQueryInput(order_number="ORD123456"))
        """
        params = input_data.dict()

        async def operation():
            order = await self.service.get_order_by_number(input_data.order_number)
            if not order:
                raise ValueError("订单不存在或订单号错误")
            return {"order": order}

        return await self._safe_execute("get_order_by_number", operation, params)

    async def get_user_orders(self, user_id: int, limit: int = 10, offset: int = 0) -> str:
        """获取用户订单列表

        查询指定用户的订单历史，支持分页查询。
        适用于用户查看历史订单、订单管理等场景。

        Args:
            user_id: 用户ID，必须为正整数
            limit: 返回数量限制，默认10条，最大50条
            offset: 偏移量，用于分页，默认0

        Returns:
            JSON格式的标准化响应，包含用户订单列表和分页信息

        Raises:
            ValueError: 当参数不符合要求时

        Example:
            >>> result = await tools.get_user_orders(123, limit=20, offset=0)
        """
        if not isinstance(user_id, int) or user_id <= 0:
            return self._create_response(False, error="用户ID必须是正整数")

        if not isinstance(limit, int) or limit <= 0 or limit > 50:
            return self._create_response(False, error="返回数量限制必须在1-50之间")

        if not isinstance(offset, int) or offset < 0:
            return self._create_response(False, error="偏移量必须为非负整数")

        params = {"user_id": user_id, "limit": limit, "offset": offset}

        async def operation():
            orders = await self.service.get_user_orders(user_id, limit, offset)
            return {
                "orders": orders,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "count": len(orders)
                }
            }

        return await self._safe_execute("get_user_orders", operation, params)

    async def track_shipment(self, tracking_number: str) -> str:
        """追踪物流配送信息

        根据物流单号查询包裹的实时配送状态和轨迹信息。
        适用于用户查询包裹位置、配送进度、预计到达时间等场景。

        Args:
            tracking_number: 物流单号，不能为空

        Returns:
            JSON格式的标准化响应，包含物流状态和轨迹信息

        Raises:
            ValueError: 当tracking_number为空时

        Example:
            >>> result = await tools.track_shipment("SF1234567890")
        """
        if not isinstance(tracking_number, str) or not tracking_number.strip():
            return self._create_response(False, error="物流单号不能为空")

        tracking_number = tracking_number.strip()
        params = {"tracking_number": tracking_number}

        async def operation():
            shipment = await self.service.track_shipment(tracking_number)
            if not shipment:
                raise ValueError("物流信息不存在或单号错误")
            return {"shipment": shipment}

        return await self._safe_execute("track_shipment", operation, params)

    # ==================== 场景4: 售后服务 - 退换货申请 ====================

    async def check_refund_eligibility(self, order_id: int, user_id: Optional[int] = None) -> str:
        """检查订单退款资格

        验证指定订单是否符合退款条件，包括时间限制、商品状态、支付状态等。
        适用于用户申请退款前的资格确认、了解退款政策等场景。

        Args:
            order_id: 订单ID，必须为正整数
            user_id: 用户ID，可选，用于权限验证

        Returns:
            JSON格式的标准化响应，包含退款资格检查结果和详细说明

        Raises:
            ValueError: 当order_id不是正整数时

        Example:
            >>> result = await tools.check_refund_eligibility(123, user_id=456)
        """
        if not isinstance(order_id, int) or order_id <= 0:
            return self._create_response(False, error="订单ID必须是正整数")

        if user_id is not None and (not isinstance(user_id, int) or user_id <= 0):
            return self._create_response(False, error="用户ID必须是正整数")

        params = {"order_id": order_id, "user_id": user_id}

        async def operation():
            result = await self.service.check_refund_eligibility(order_id, user_id)
            return {"eligibility": result}

        return await self._safe_execute("check_refund_eligibility", operation, params)

    async def create_refund_request(self, input_data: RefundRequestInput) -> str:
        """创建退款申请

        提交新的退款申请，包括退款原因、金额、详细说明等信息。
        适用于用户申请退款、处理商品质量问题等场景。

        Args:
            input_data: 退款申请参数，包含订单ID、用户ID、退款原因等

        Returns:
            JSON格式的标准化响应，包含退款申请结果和申请单号

        Example:
            >>> result = await tools.create_refund_request(RefundRequestInput(
            ...     order_id=123, user_id=456, reason=RefundReason.QUALITY_ISSUE
            ... ))
        """
        params = input_data.dict()

        async def operation():
            result = await self.service.create_refund_request(
                order_id=input_data.order_id,
                user_id=input_data.user_id,
                reason=input_data.reason.value,
                amount=input_data.amount
            )
            return {"refund_request": result}

        return await self._safe_execute("create_refund_request", operation, params)

    async def get_refund_status(self, refund_number: str, user_id: Optional[int] = None) -> str:
        """获取退款申请状态

        查询指定退款申请的处理状态和进度信息。
        适用于用户查询退款进度、了解退款状态等场景。

        Args:
            refund_number: 退款单号，不能为空
            user_id: 用户ID，可选，用于权限验证

        Returns:
            JSON格式的标准化响应，包含退款状态和处理进度

        Raises:
            ValueError: 当refund_number为空或user_id格式错误时

        Example:
            >>> result = await tools.get_refund_status("REF123456", user_id=456)
        """
        if not isinstance(refund_number, str) or not refund_number.strip():
            return self._create_response(False, error="退款单号不能为空")

        if user_id is not None and (not isinstance(user_id, int) or user_id <= 0):
            return self._create_response(False, error="用户ID必须是正整数")

        refund_number = refund_number.strip()
        params = {"refund_number": refund_number, "user_id": user_id}

        async def operation():
            refund = await self.service.get_refund_status(refund_number, user_id)
            if not refund:
                raise ValueError("退款记录不存在或单号错误")
            return {"refund": refund}

        return await self._safe_execute("get_refund_status", operation, params)

    # ==================== 场景5: 投诉与建议处理 ====================

    async def create_complaint(self, input_data: ComplaintInput) -> str:
        """创建用户投诉

        提交新的用户投诉，包括投诉分类、主题、详细描述等信息。
        适用于用户对产品质量、服务态度、配送问题等进行投诉的场景。

        Args:
            input_data: 投诉参数，包含用户ID、投诉分类、主题、描述等

        Returns:
            JSON格式的标准化响应，包含投诉创建结果和投诉单号

        Example:
            >>> result = await tools.create_complaint(ComplaintInput(
            ...     user_id=123, category=ComplaintCategory.PRODUCT_QUALITY,
            ...     subject="商品质量问题", description="收到的商品有明显瑕疵"
            ... ))
        """
        params = input_data.dict()

        async def operation():
            result = await self.service.create_complaint(
                user_id=input_data.user_id,
                category=input_data.category.value,
                subject=input_data.subject,
                description=input_data.description,
                order_id=input_data.order_id
            )
            return {"complaint": result}

        return await self._safe_execute("create_complaint", operation, params)

    async def get_complaint_status(self, complaint_number: str, user_id: Optional[int] = None) -> str:
        """获取投诉处理状态

        查询指定投诉的处理状态和进度信息。
        适用于用户查询投诉进度、了解处理结果等场景。

        Args:
            complaint_number: 投诉单号，不能为空
            user_id: 用户ID，可选，用于权限验证

        Returns:
            JSON格式的标准化响应，包含投诉状态和处理进度

        Raises:
            ValueError: 当complaint_number为空或user_id格式错误时

        Example:
            >>> result = await tools.get_complaint_status("CMP123456", user_id=123)
        """
        if not isinstance(complaint_number, str) or not complaint_number.strip():
            return self._create_response(False, error="投诉单号不能为空")

        if user_id is not None and (not isinstance(user_id, int) or user_id <= 0):
            return self._create_response(False, error="用户ID必须是正整数")

        complaint_number = complaint_number.strip()
        params = {"complaint_number": complaint_number, "user_id": user_id}

        async def operation():
            complaint = await self.service.get_complaint_status(complaint_number, user_id)
            if not complaint:
                raise ValueError("投诉记录不存在或单号错误")
            return {"complaint": complaint}

        return await self._safe_execute("get_complaint_status", operation, params)

    async def search_faq(self, query: str, category_id: Optional[int] = None, limit: int = 10) -> str:
        """搜索常见问题解答

        根据关键词搜索相关的FAQ内容，帮助用户快速找到问题答案。
        适用于用户咨询常见问题、寻求帮助信息等场景。

        Args:
            query: 搜索关键词，不能为空，至少2个字符
            category_id: FAQ分类ID，可选，用于按分类筛选
            limit: 返回数量限制，默认10条，最大20条

        Returns:
            JSON格式的标准化响应，包含匹配的FAQ列表

        Raises:
            ValueError: 当参数不符合要求时

        Example:
            >>> result = await tools.search_faq("退货政策", category_id=1, limit=5)
        """
        if not isinstance(query, str) or len(query.strip()) < 2:
            return self._create_response(False, error="搜索关键词至少包含2个字符")

        if category_id is not None and (not isinstance(category_id, int) or category_id <= 0):
            return self._create_response(False, error="分类ID必须是正整数")

        if not isinstance(limit, int) or limit <= 0 or limit > 20:
            return self._create_response(False, error="返回数量限制必须在1-20之间")

        query = query.strip()
        params = {"query": query, "category_id": category_id, "limit": limit}

        async def operation():
            faqs = await self.service.search_faq(query, category_id, limit)
            return {
                "faqs": faqs,
                "search_query": query,
                "total_found": len(faqs)
            }

        return await self._safe_execute("search_faq", operation, params)

    # ==================== 场景6: 人工客服服务转接 ====================

    async def request_human_agent(self, input_data: HumanAgentRequestInput) -> str:
        """请求人工客服服务

        将用户转接到人工客服，适用于复杂问题、特殊需求等AI无法处理的场景。
        系统会根据客服工作负载自动分配或加入等待队列。

        Args:
            input_data: 人工客服请求参数，包含用户ID、对话ID、请求原因等

        Returns:
            JSON格式的标准化响应，包含转接结果和等待信息

        Example:
            >>> result = await tools.request_human_agent(HumanAgentRequestInput(
            ...     user_id=123, conversation_id="conv_456", reason="复杂技术问题需要专业解答"
            ... ))
        """
        params = input_data.dict()

        async def operation():
            result = await self.service.request_human_agent(
                user_id=input_data.user_id,
                conversation_id=input_data.conversation_id,
                reason=input_data.reason
            )
            return {"human_agent_request": result}

        return await self._safe_execute("request_human_agent", operation, params)

    async def check_human_queue_status(self, conversation_id: str, user_id: int) -> str:
        """检查人工客服队列状态

        查询用户在人工客服等待队列中的位置和预计等待时间。
        适用于用户查询排队状态、了解等待时长等场景。

        Args:
            conversation_id: 对话ID，不能为空
            user_id: 用户ID，必须为正整数

        Returns:
            JSON格式的标准化响应，包含队列位置和等待时间信息

        Raises:
            ValueError: 当参数格式错误时

        Example:
            >>> result = await tools.check_human_queue_status("conv_456", 123)
        """
        if not isinstance(conversation_id, str) or not conversation_id.strip():
            return self._create_response(False, error="对话ID不能为空")

        if not isinstance(user_id, int) or user_id <= 0:
            return self._create_response(False, error="用户ID必须是正整数")

        conversation_id = conversation_id.strip()
        params = {"conversation_id": conversation_id, "user_id": user_id}

        async def operation():
            result = await self.service.check_human_queue_status(conversation_id, user_id)
            return {"queue_status": result}

        return await self._safe_execute("check_human_queue_status", operation, params)

    # ==================== 工具定义接口 ====================

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表（供AI模型调用）

        返回所有可用的客服工具定义，包含详细的参数说明和使用示例。
        AI模型可以根据这些定义选择合适的工具来处理用户请求。

        Returns:
            List[Dict]: 标准化的工具定义列表，符合OpenAI Function Calling格式

        Example:
            >>> tools = CustomerServiceTools(db)
            >>> tool_definitions = tools.get_available_tools()
            >>> print(f"可用工具数量: {len(tool_definitions)}")
        """
        return [
            # ==================== 场景1: 产品信息查询 ====================
            {
                "name": "search_products",
                "description": "搜索产品信息，支持关键词、分类、价格范围等多种筛选条件。适用于用户询问产品信息、比较产品、查找特定商品等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "搜索关键词，支持产品名称、描述等模糊匹配",
                            "minLength": 1,
                            "maxLength": 100
                        },
                        "category_id": {
                            "type": "integer",
                            "description": "产品分类ID，用于按分类筛选",
                            "minimum": 1
                        },
                        "min_price": {
                            "type": "number",
                            "description": "最低价格筛选条件",
                            "minimum": 0
                        },
                        "max_price": {
                            "type": "number",
                            "description": "最高价格筛选条件",
                            "minimum": 0
                        },
                        "in_stock": {
                            "type": "boolean",
                            "description": "是否只显示有库存商品",
                            "default": True
                        },
                        "limit": {
                            "type": "integer",
                            "description": "返回结果数量限制",
                            "default": 10,
                            "minimum": 1,
                            "maximum": 50
                        }
                    }
                }
            },
            {
                "name": "get_product_details",
                "description": "根据产品ID获取完整的产品信息，包括规格、价格、库存、评价等。适用于用户询问特定产品详情、产品参数对比等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "product_id": {
                            "type": "integer",
                            "description": "产品ID，必须为正整数",
                            "minimum": 1
                        }
                    },
                    "required": ["product_id"]
                }
            },
            {
                "name": "check_product_stock",
                "description": "查询指定产品的实时库存信息，包括可用数量、预留数量等。适用于用户询问商品是否有货、库存数量等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "product_id": {
                            "type": "integer",
                            "description": "产品ID，必须为正整数",
                            "minimum": 1
                        }
                    },
                    "required": ["product_id"]
                }
            },
            # ==================== 场景2: 活动与优惠查询 ====================
            {
                "name": "get_active_promotions",
                "description": "查询当前正在进行的所有促销活动，包括折扣、满减、买赠等。适用于用户询问当前优惠活动、促销信息等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "additionalProperties": False
                }
            },
            {
                "name": "get_user_coupons",
                "description": "查询指定用户的优惠券，可选择只显示可用的优惠券。适用于用户查询自己的优惠券、了解可用优惠等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，必须为正整数",
                            "minimum": 1
                        },
                        "available_only": {
                            "type": "boolean",
                            "description": "是否只返回可用优惠券",
                            "default": True
                        }
                    },
                    "required": ["user_id"]
                }
            },
            {
                "name": "validate_coupon",
                "description": "验证指定优惠券代码的有效性，包括是否存在、是否过期、使用条件等。适用于用户使用优惠券前的验证、了解优惠券使用规则等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "code": {
                            "type": "string",
                            "description": "优惠券代码，不能为空",
                            "minLength": 1,
                            "maxLength": 50
                        },
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，用于验证用户权限",
                            "minimum": 1
                        },
                        "order_amount": {
                            "type": "number",
                            "description": "订单金额，用于计算优惠",
                            "minimum": 0
                        }
                    },
                    "required": ["code"]
                }
            },
            {
                "name": "get_membership_benefits",
                "description": "查询指定用户的会员等级和对应权益，包括折扣、积分倍率、专属优惠等。适用于用户询问会员权益、了解会员等级等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，必须为正整数",
                            "minimum": 1
                        }
                    },
                    "required": ["user_id"]
                }
            },
            # ==================== 场景3: 订单追踪与管理 ====================
            {
                "name": "get_order_by_number",
                "description": "通过订单号查询完整的订单信息，包括商品详情、支付状态、物流信息等。适用于用户查询订单状态、订单详情、物流进度等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "order_number": {
                            "type": "string",
                            "description": "订单号，用于查询订单详细信息",
                            "minLength": 1,
                            "maxLength": 50
                        }
                    },
                    "required": ["order_number"]
                }
            },
            {
                "name": "get_user_orders",
                "description": "查询指定用户的订单历史，支持分页查询。适用于用户查看历史订单、订单管理等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，必须为正整数",
                            "minimum": 1
                        },
                        "limit": {
                            "type": "integer",
                            "description": "返回数量限制，默认10条，最大50条",
                            "default": 10,
                            "minimum": 1,
                            "maximum": 50
                        },
                        "offset": {
                            "type": "integer",
                            "description": "偏移量，用于分页，默认0",
                            "default": 0,
                            "minimum": 0
                        }
                    },
                    "required": ["user_id"]
                }
            },
            {
                "name": "track_shipment",
                "description": "根据物流单号查询包裹的实时配送状态和轨迹信息。适用于用户查询包裹位置、配送进度、预计到达时间等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "tracking_number": {
                            "type": "string",
                            "description": "物流单号，不能为空",
                            "minLength": 1
                        }
                    },
                    "required": ["tracking_number"]
                }
            },
            # ==================== 场景4: 售后服务 - 退换货申请 ====================
            {
                "name": "check_refund_eligibility",
                "description": "验证指定订单是否符合退款条件，包括时间限制、商品状态、支付状态等。适用于用户申请退款前的资格确认、了解退款政策等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "order_id": {
                            "type": "integer",
                            "description": "订单ID，必须为正整数",
                            "minimum": 1
                        },
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，可选，用于权限验证",
                            "minimum": 1
                        }
                    },
                    "required": ["order_id"]
                }
            },
            {
                "name": "create_refund_request",
                "description": "提交新的退款申请，包括退款原因、金额、详细说明等信息。适用于用户申请退款、处理商品质量问题等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "order_id": {
                            "type": "integer",
                            "description": "订单ID，必须为正整数",
                            "minimum": 1
                        },
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，必须为正整数",
                            "minimum": 1
                        },
                        "reason": {
                            "type": "string",
                            "description": "退款原因，必须从预定义选项中选择",
                            "enum": ["quality_issue", "size_mismatch", "not_as_described", "damaged_shipping", "changed_mind", "other"]
                        },
                        "amount": {
                            "type": "number",
                            "description": "退款金额，不填则全额退款",
                            "minimum": 0
                        },
                        "description": {
                            "type": "string",
                            "description": "详细说明",
                            "maxLength": 500
                        }
                    },
                    "required": ["order_id", "user_id", "reason"]
                }
            },
            {
                "name": "get_refund_status",
                "description": "查询指定退款申请的处理状态和进度信息。适用于用户查询退款进度、了解退款状态等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "refund_number": {
                            "type": "string",
                            "description": "退款单号，不能为空",
                            "minLength": 1
                        },
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，可选，用于权限验证",
                            "minimum": 1
                        }
                    },
                    "required": ["refund_number"]
                }
            },
            # ==================== 场景5: 投诉与建议处理 ====================
            {
                "name": "create_complaint",
                "description": "提交新的用户投诉，包括投诉分类、主题、详细描述等信息。适用于用户对产品质量、服务态度、配送问题等进行投诉的场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，必须为正整数",
                            "minimum": 1
                        },
                        "category": {
                            "type": "string",
                            "description": "投诉分类，必须从预定义选项中选择",
                            "enum": ["product_quality", "service_attitude", "delivery_issue", "payment_issue", "refund_issue", "other"]
                        },
                        "subject": {
                            "type": "string",
                            "description": "投诉主题",
                            "minLength": 1,
                            "maxLength": 100
                        },
                        "description": {
                            "type": "string",
                            "description": "投诉详细描述",
                            "minLength": 10,
                            "maxLength": 1000
                        },
                        "order_id": {
                            "type": "integer",
                            "description": "关联订单ID，可选",
                            "minimum": 1
                        }
                    },
                    "required": ["user_id", "category", "subject", "description"]
                }
            },
            {
                "name": "get_complaint_status",
                "description": "查询指定投诉的处理状态和进度信息。适用于用户查询投诉进度、了解处理结果等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "complaint_number": {
                            "type": "string",
                            "description": "投诉单号，不能为空",
                            "minLength": 1
                        },
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，可选，用于权限验证",
                            "minimum": 1
                        }
                    },
                    "required": ["complaint_number"]
                }
            },
            {
                "name": "search_faq",
                "description": "根据关键词搜索相关的FAQ内容，帮助用户快速找到问题答案。适用于用户咨询常见问题、寻求帮助信息等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "搜索关键词，不能为空，至少2个字符",
                            "minLength": 2
                        },
                        "category_id": {
                            "type": "integer",
                            "description": "FAQ分类ID，可选，用于按分类筛选",
                            "minimum": 1
                        },
                        "limit": {
                            "type": "integer",
                            "description": "返回数量限制，默认10条，最大20条",
                            "default": 10,
                            "minimum": 1,
                            "maximum": 20
                        }
                    },
                    "required": ["query"]
                }
            },
            # ==================== 场景6: 人工客服服务转接 ====================
            {
                "name": "request_human_agent",
                "description": "将用户转接到人工客服，适用于复杂问题、特殊需求等AI无法处理的场景。系统会根据客服工作负载自动分配或加入等待队列。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，必须为正整数",
                            "minimum": 1
                        },
                        "conversation_id": {
                            "type": "string",
                            "description": "对话ID，用于标识会话",
                            "minLength": 1,
                            "maxLength": 100
                        },
                        "reason": {
                            "type": "string",
                            "description": "请求人工客服的原因",
                            "maxLength": 200
                        }
                    },
                    "required": ["user_id", "conversation_id"]
                }
            },
            {
                "name": "check_human_queue_status",
                "description": "查询用户在人工客服等待队列中的位置和预计等待时间。适用于用户查询排队状态、了解等待时长等场景。",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "conversation_id": {
                            "type": "string",
                            "description": "对话ID，不能为空",
                            "minLength": 1
                        },
                        "user_id": {
                            "type": "integer",
                            "description": "用户ID，必须为正整数",
                            "minimum": 1
                        }
                    },
                    "required": ["conversation_id", "user_id"]
                }
            }
        ]
