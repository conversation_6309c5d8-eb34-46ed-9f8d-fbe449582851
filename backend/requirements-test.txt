# YUE商城客服工具测试依赖

# 核心测试框架
pytest>=6.0.0
pytest-asyncio>=0.21.0

# 测试增强功能
pytest-cov>=4.0.0          # 代码覆盖率
pytest-html>=3.1.0         # HTML测试报告
pytest-mock>=3.10.0        # Mock功能增强
pytest-xdist>=3.0.0        # 并行测试

# 断言和匹配
pytest-benchmark>=4.0.0    # 性能基准测试
pytest-timeout>=2.1.0      # 测试超时控制

# 代码质量检查
flake8>=5.0.0              # 代码风格检查
black>=22.0.0              # 代码格式化
isort>=5.10.0              # 导入排序

# 类型检查
mypy>=0.991                # 静态类型检查

# 测试数据生成
factory-boy>=3.2.0         # 测试数据工厂
faker>=15.0.0              # 假数据生成

# 异步测试支持
asynctest>=0.13.0          # 异步测试工具
aioresponses>=0.7.0        # HTTP异步响应模拟
