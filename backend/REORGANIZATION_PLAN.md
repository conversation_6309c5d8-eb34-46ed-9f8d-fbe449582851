# Backend 目录重新整理方案

## 🎯 整理目标

1. **分离关注点**：将配置、文档、测试、脚本等分开
2. **统一命名**：使用一致的命名规范  
3. **清晰层次**：建立清晰的目录层次结构
4. **便于维护**：让代码更容易理解和维护

## 📁 新的目录结构

```
backend/
├── 📁 app/                          # 核心应用代码
│   ├── 📁 api/                      # API路由层
│   │   ├── __init__.py
│   │   ├── customer_service.py      # 客服API
│   │   ├── auth.py                  # 认证API (待添加)
│   │   └── health.py                # 健康检查API (待添加)
│   ├── 📁 core/                     # 核心配置和工具
│   │   ├── __init__.py
│   │   ├── config.py                # 配置管理
│   │   ├── llms.py                  # LLM配置
│   │   ├── security.py              # 安全相关 (待添加)
│   │   └── logging.py               # 日志配置 (待添加)
│   ├── 📁 db/                       # 数据库层
│   │   ├── __init__.py
│   │   ├── database.py              # 数据库连接
│   │   └── init_db.py               # 数据库初始化
│   ├── 📁 models/                   # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py                  # 基础模型
│   │   ├── user.py                  # 用户相关模型
│   │   ├── product.py               # 产品相关模型
│   │   ├── order.py                 # 订单相关模型
│   │   ├── promotion.py             # 促销相关模型
│   │   └── complaint.py             # 投诉相关模型
│   ├── 📁 services/                 # 业务逻辑层
│   │   ├── __init__.py
│   │   ├── customer_service.py      # 客服业务逻辑
│   │   └── chat_service.py          # 聊天服务
│   ├── 📁 tools/                    # 工具和集成
│   │   ├── __init__.py
│   │   └── customer_service_tools.py # 客服工具
│   ├── 📁 utils/                    # 通用工具函数
│   │   ├── __init__.py
│   │   ├── validators.py            # 验证器 (待添加)
│   │   └── helpers.py               # 辅助函数 (待添加)
│   └── main.py                      # 应用入口
├── 📁 tests/                        # 测试代码
│   ├── __init__.py
│   ├── README.md                    # 测试文档
│   ├── conftest.py                  # 测试配置 (待添加)
│   ├── test_customer_service_tools.py # 客服工具测试
│   └── 📁 integration/              # 集成测试 (待添加)
├── 📁 scripts/                      # 脚本文件
│   ├── start_server.py              # 服务器启动脚本
│   ├── init_database.py             # 数据库初始化脚本
│   ├── quick_start.py               # 快速启动脚本
│   └── run_tests.py                 # 测试运行脚本
├── 📁 docs/                         # 文档
│   ├── README.md                    # 主要文档
│   ├── CONFIG_GUIDE.md              # 配置指南
│   ├── SQLITE_SETUP_GUIDE.md        # SQLite设置指南
│   └── CUSTOMER_SERVICE_README.md   # 客服系统文档
├── 📁 config/                       # 配置文件
│   ├── .env                         # 环境变量
│   ├── .env.example                 # 环境变量示例
│   └── pytest.ini                  # pytest配置
├── 📁 data/                         # 数据文件
│   └── yue_platform.db              # SQLite数据库文件
├── 📁 logs/                         # 日志文件 (待创建)
├── requirements.txt                 # 生产依赖
├── requirements-test.txt            # 测试依赖
└── 📁 temp/                         # 临时文件和测试文件
    ├── demo_customer_service.py     # 演示脚本
    ├── test_config.py               # 配置测试
    ├── test_llm_config.py           # LLM配置测试
    ├── test_sqlite.py               # SQLite测试
    ├── test_customer_service.py     # 客服测试
    └── test_results.md              # 测试结果
```

## 🔄 迁移步骤

### 第一步：创建新目录结构
1. 创建 `scripts/`, `docs/`, `config/`, `data/`, `logs/`, `temp/` 目录
2. 移动相应文件到新目录

### 第二步：移动文件
1. **脚本文件** → `scripts/`
   - `start_server.py`
   - `init_database.py` 
   - `quick_start.py`
   - `run_tests.py`

2. **文档文件** → `docs/`
   - `README.md`
   - `CONFIG_GUIDE.md`
   - `SQLITE_SETUP_GUIDE.md`
   - `CUSTOMER_SERVICE_README.md`

3. **配置文件** → `config/`
   - `.env`
   - `pytest.ini`
   - 创建 `.env.example`

4. **数据文件** → `data/`
   - `yue_platform.db`

5. **临时/测试文件** → `temp/`
   - `demo_customer_service.py`
   - `test_*.py` (除了 `tests/` 目录下的)
   - `test_results.md`

### 第三步：更新导入路径
1. 更新所有文件中的导入路径
2. 更新配置文件中的路径引用
3. 更新脚本中的相对路径

### 第四步：清理和优化
1. 删除重复文件
2. 统一命名规范
3. 更新文档

## 📋 文件分类详情

### 🏗️ 核心应用代码 (`app/`)
- 保持现有结构，这部分组织良好
- 所有业务逻辑、API、模型都在这里

### 🧪 测试代码 (`tests/`)
- 保持现有测试文件
- 添加集成测试目录
- 添加测试配置文件

### 📜 脚本文件 (`scripts/`)
- 所有可执行脚本
- 启动、初始化、测试脚本

### 📚 文档 (`docs/`)
- 所有文档和指南
- API文档、配置指南等

### ⚙️ 配置 (`config/`)
- 环境变量和配置文件
- 测试配置

### 💾 数据 (`data/`)
- 数据库文件
- 静态数据文件

### 🗂️ 临时文件 (`temp/`)
- 演示脚本
- 临时测试文件
- 测试结果

## ✅ 整理后的优势

1. **清晰的职责分离**：每个目录都有明确的用途
2. **易于导航**：开发者可以快速找到需要的文件
3. **便于维护**：相关文件集中管理
4. **标准化结构**：符合Python项目最佳实践
5. **可扩展性**：为未来功能预留空间
