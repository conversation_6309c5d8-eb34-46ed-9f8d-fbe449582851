"""
YUE商城智能客服工具测试

本模块包含对 CustomerServiceTools 类的全面测试，覆盖六大核心场景：
1. 售前咨询 - 产品信息查询
2. 售前咨询 - 活动与优惠查询
3. 订单追踪与管理
4. 售后服务 - 退换货申请
5. 投诉与建议处理
6. 人工客服服务转接

测试框架: pytest + pytest-asyncio
测试覆盖: 单元测试、集成测试、异常处理测试
"""

import pytest
import pytest_asyncio
import json
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any

# 导入被测试的模块
from app.tools.customer_service_tools import (
    CustomerServiceTools,
    ProductSearchInput,
    OrderQueryInput,
    CouponValidationInput,
    RefundRequestInput,
    ComplaintInput,
    HumanAgentRequestInput,
    RefundReason,
    ComplaintCategory,
    ToolResponse
)
from app.services.customer_service import CustomerServiceService


# ================================================================================================
# 测试夹具 (Fixtures)
# ================================================================================================

@pytest_asyncio.fixture
async def mock_db_session():
    """模拟数据库会话"""
    mock_session = AsyncMock()
    return mock_session


@pytest_asyncio.fixture
async def mock_customer_service():
    """模拟客服服务"""
    mock_service = AsyncMock(spec=CustomerServiceService)
    return mock_service


@pytest_asyncio.fixture
async def customer_service_tools(mock_db_session, mock_customer_service):
    """创建客服工具实例"""
    tools = CustomerServiceTools(mock_db_session)
    tools.service = mock_customer_service
    return tools


@pytest.fixture
def sample_product_data():
    """示例产品数据"""
    return [
        {
            "id": 1,
            "name": "iPhone 15 Pro",
            "price": 7999.0,
            "stock_quantity": 50,
            "category_id": 1,
            "description": "最新款iPhone，配备A17 Pro芯片"
        },
        {
            "id": 2,
            "name": "iPhone 15",
            "price": 5999.0,
            "stock_quantity": 30,
            "category_id": 1,
            "description": "iPhone 15标准版"
        }
    ]


@pytest.fixture
def sample_order_data():
    """示例订单数据"""
    return {
        "id": 1,
        "order_number": "ORD20241219001",
        "user_id": 1,
        "status": "shipped",
        "total_amount": 7999.0,
        "created_at": "2024-12-19T10:00:00",
        "items": [
            {
                "product_id": 1,
                "product_name": "iPhone 15 Pro",
                "quantity": 1,
                "price": 7999.0
            }
        ]
    }


@pytest.fixture
def sample_promotion_data():
    """示例促销数据"""
    return [
        {
            "id": 1,
            "name": "双12大促",
            "description": "全场8折优惠",
            "discount_type": "percentage",
            "discount_value": 20.0,
            "start_date": "2024-12-01T00:00:00",
            "end_date": "2024-12-31T23:59:59"
        }
    ]


# ================================================================================================
# 辅助函数测试
# ================================================================================================

class TestCustomerServiceToolsHelpers:
    """测试CustomerServiceTools的辅助方法"""

    def test_create_response_success(self, customer_service_tools):
        """测试成功响应创建"""
        data = {"test": "data"}
        response = customer_service_tools._create_response(
            success=True,
            data=data,
            message="操作成功"
        )
        
        response_dict = json.loads(response)
        assert response_dict["success"] is True
        assert response_dict["data"] == data
        assert response_dict["message"] == "操作成功"
        assert response_dict["error"] is None
        assert "timestamp" in response_dict

    def test_create_response_error(self, customer_service_tools):
        """测试错误响应创建"""
        error_msg = "操作失败"
        response = customer_service_tools._create_response(
            success=False,
            error=error_msg
        )
        
        response_dict = json.loads(response)
        assert response_dict["success"] is False
        assert response_dict["error"] == error_msg
        assert response_dict["data"] is None
        assert "timestamp" in response_dict

    @patch('app.tools.customer_service_tools.logger')
    def test_log_tool_call_success(self, mock_logger, customer_service_tools):
        """测试成功日志记录"""
        customer_service_tools._log_tool_call(
            "test_tool",
            {"param": "value"},
            True
        )
        
        mock_logger.info.assert_called_once()
        call_args = mock_logger.info.call_args[0]
        assert "✅ 工具调用成功: test_tool" in call_args[0]

    @patch('app.tools.customer_service_tools.logger')
    def test_log_tool_call_error(self, mock_logger, customer_service_tools):
        """测试错误日志记录"""
        customer_service_tools._log_tool_call(
            "test_tool",
            {"param": "value"},
            False,
            "测试错误"
        )
        
        mock_logger.error.assert_called_once()
        call_args = mock_logger.error.call_args[0]
        assert "❌ 工具调用失败: test_tool - 测试错误" in call_args[0]


# ================================================================================================
# 输入模型验证测试
# ================================================================================================

class TestInputModels:
    """测试输入模型的验证逻辑"""

    def test_product_search_input_valid(self):
        """测试有效的产品搜索输入"""
        input_data = ProductSearchInput(
            query="iPhone",
            category_id=1,
            min_price=1000.0,
            max_price=10000.0,
            in_stock=True,
            limit=10
        )
        assert input_data.query == "iPhone"
        assert input_data.category_id == 1
        assert input_data.min_price == 1000.0
        assert input_data.max_price == 10000.0

    def test_product_search_input_price_validation(self):
        """测试价格范围验证"""
        with pytest.raises(ValueError, match="最高价格不能小于最低价格"):
            ProductSearchInput(
                min_price=10000.0,
                max_price=1000.0
            )

    def test_order_query_input_valid(self):
        """测试有效的订单查询输入"""
        input_data = OrderQueryInput(order_number="  ORD123456  ")
        assert input_data.order_number == "ORD123456"

    def test_order_query_input_empty(self):
        """测试空订单号验证"""
        with pytest.raises(ValueError, match="订单号不能为空"):
            OrderQueryInput(order_number="   ")

    def test_coupon_validation_input_valid(self):
        """测试有效的优惠券验证输入"""
        input_data = CouponValidationInput(
            code="  save20  ",
            user_id=123,
            order_amount=1000.0
        )
        assert input_data.code == "SAVE20"
        assert input_data.user_id == 123

    def test_refund_request_input_valid(self):
        """测试有效的退款申请输入"""
        input_data = RefundRequestInput(
            order_id=123,
            user_id=456,
            reason=RefundReason.QUALITY_ISSUE,
            amount=999.0,
            description="商品有质量问题"
        )
        assert input_data.order_id == 123
        assert input_data.reason == RefundReason.QUALITY_ISSUE

    def test_complaint_input_valid(self):
        """测试有效的投诉输入"""
        input_data = ComplaintInput(
            user_id=123,
            category=ComplaintCategory.PRODUCT_QUALITY,
            subject="  商品质量问题  ",
            description="  收到的商品有明显瑕疵，要求退换货  ",
            order_id=456
        )
        assert input_data.subject == "商品质量问题"
        assert input_data.description == "收到的商品有明显瑕疵，要求退换货"

    def test_human_agent_request_input_valid(self):
        """测试有效的人工客服请求输入"""
        input_data = HumanAgentRequestInput(
            user_id=123,
            conversation_id="  conv_456  ",
            reason="复杂技术问题需要专业解答"
        )
        assert input_data.conversation_id == "conv_456"
        assert input_data.reason == "复杂技术问题需要专业解答"


# ================================================================================================
# 场景1: 产品信息查询测试
# ================================================================================================

class TestProductInformationScenario:
    """测试场景1: 售前咨询 - 产品信息查询"""

    @pytest.mark.asyncio
    async def test_search_products_success(self, customer_service_tools, sample_product_data):
        """测试成功搜索产品"""
        # 配置mock
        customer_service_tools.service.search_products.return_value = sample_product_data
        
        # 执行测试
        input_data = ProductSearchInput(query="iPhone", limit=5)
        result = await customer_service_tools.search_products(input_data)
        
        # 验证结果
        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert len(response_dict["data"]["products"]) == 2
        assert response_dict["data"]["total_found"] == 2
        assert "search_params" in response_dict["data"]
        
        # 验证服务调用
        customer_service_tools.service.search_products.assert_called_once_with(
            query="iPhone",
            category_id=None,
            min_price=None,
            max_price=None,
            in_stock=True,
            limit=5
        )

    @pytest.mark.asyncio
    async def test_search_products_with_filters(self, customer_service_tools, sample_product_data):
        """测试带筛选条件的产品搜索"""
        customer_service_tools.service.search_products.return_value = sample_product_data
        
        input_data = ProductSearchInput(
            query="iPhone",
            category_id=1,
            min_price=5000.0,
            max_price=10000.0,
            in_stock=True,
            limit=10
        )
        result = await customer_service_tools.search_products(input_data)
        
        response_dict = json.loads(result)
        assert response_dict["success"] is True
        
        # 验证服务调用参数
        customer_service_tools.service.search_products.assert_called_once_with(
            query="iPhone",
            category_id=1,
            min_price=5000.0,
            max_price=10000.0,
            in_stock=True,
            limit=10
        )

    @pytest.mark.asyncio
    async def test_search_products_service_error(self, customer_service_tools):
        """测试产品搜索服务异常"""
        # 配置mock抛出异常
        customer_service_tools.service.search_products.side_effect = Exception("数据库连接失败")

        input_data = ProductSearchInput(query="iPhone")
        result = await customer_service_tools.search_products(input_data)

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "系统错误" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_get_product_details_success(self, customer_service_tools):
        """测试成功获取产品详情"""
        product_detail = {
            "id": 1,
            "name": "iPhone 15 Pro",
            "price": 7999.0,
            "specifications": {"color": "深空黑", "storage": "256GB"},
            "reviews_count": 150,
            "average_rating": 4.8
        }
        customer_service_tools.service.get_product_details.return_value = product_detail

        result = await customer_service_tools.get_product_details(1)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["product"]["name"] == "iPhone 15 Pro"
        assert response_dict["data"]["product"]["specifications"]["color"] == "深空黑"

    @pytest.mark.asyncio
    async def test_get_product_details_invalid_id(self, customer_service_tools):
        """测试无效产品ID"""
        result = await customer_service_tools.get_product_details(-1)

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "产品ID必须是正整数" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_get_product_details_not_found(self, customer_service_tools):
        """测试产品不存在"""
        customer_service_tools.service.get_product_details.return_value = None

        result = await customer_service_tools.get_product_details(999)

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "产品不存在或已下架" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_check_product_stock_success(self, customer_service_tools):
        """测试成功检查产品库存"""
        stock_info = {
            "product_id": 1,
            "available_quantity": 50,
            "reserved_quantity": 5,
            "in_stock": True,
            "low_stock_warning": False
        }
        customer_service_tools.service.check_product_stock.return_value = stock_info

        result = await customer_service_tools.check_product_stock(1)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["stock"]["available_quantity"] == 50
        assert response_dict["data"]["stock"]["in_stock"] is True

    @pytest.mark.asyncio
    async def test_check_product_stock_invalid_id(self, customer_service_tools):
        """测试无效产品ID的库存检查"""
        result = await customer_service_tools.check_product_stock(0)

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "产品ID必须是正整数" in response_dict["error"]


# ================================================================================================
# 场景2: 活动与优惠查询测试
# ================================================================================================

class TestPromotionScenario:
    """测试场景2: 售前咨询 - 活动与优惠查询"""

    @pytest.mark.asyncio
    async def test_get_active_promotions_success(self, customer_service_tools, sample_promotion_data):
        """测试成功获取活跃促销活动"""
        customer_service_tools.service.get_active_promotions.return_value = sample_promotion_data

        result = await customer_service_tools.get_active_promotions()

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert len(response_dict["data"]["promotions"]) == 1
        assert response_dict["data"]["total_count"] == 1
        assert "query_time" in response_dict["data"]

    @pytest.mark.asyncio
    async def test_get_active_promotions_empty(self, customer_service_tools):
        """测试无活跃促销活动"""
        customer_service_tools.service.get_active_promotions.return_value = []

        result = await customer_service_tools.get_active_promotions()

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["total_count"] == 0

    @pytest.mark.asyncio
    async def test_get_user_coupons_success(self, customer_service_tools):
        """测试成功获取用户优惠券"""
        coupons_data = [
            {
                "id": 1,
                "code": "SAVE20",
                "discount_value": 20.0,
                "min_order_amount": 100.0,
                "expires_at": "2024-12-31T23:59:59",
                "is_used": False
            }
        ]
        customer_service_tools.service.get_user_coupons.return_value = coupons_data

        result = await customer_service_tools.get_user_coupons(123, available_only=True)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert len(response_dict["data"]["coupons"]) == 1
        assert response_dict["data"]["available_only"] is True

    @pytest.mark.asyncio
    async def test_get_user_coupons_invalid_user_id(self, customer_service_tools):
        """测试无效用户ID"""
        result = await customer_service_tools.get_user_coupons(-1)

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "用户ID必须是正整数" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_validate_coupon_success(self, customer_service_tools):
        """测试成功验证优惠券"""
        validation_result = {
            "valid": True,
            "discount_value": 20.0,
            "min_order_amount": 100.0,
            "applicable": True,
            "message": "优惠券有效"
        }
        customer_service_tools.service.validate_coupon.return_value = validation_result

        input_data = CouponValidationInput(
            code="SAVE20",
            user_id=123,
            order_amount=200.0
        )
        result = await customer_service_tools.validate_coupon(input_data)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["validation"]["valid"] is True

    @pytest.mark.asyncio
    async def test_validate_coupon_invalid(self, customer_service_tools):
        """测试无效优惠券"""
        validation_result = {
            "valid": False,
            "reason": "优惠券已过期",
            "applicable": False
        }
        customer_service_tools.service.validate_coupon.return_value = validation_result

        input_data = CouponValidationInput(code="EXPIRED20")
        result = await customer_service_tools.validate_coupon(input_data)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["validation"]["valid"] is False

    @pytest.mark.asyncio
    async def test_get_membership_benefits_success(self, customer_service_tools):
        """测试成功获取会员权益"""
        benefits_data = {
            "user_id": 123,
            "membership_level": "Gold",
            "discount_rate": 0.1,
            "points_multiplier": 2.0,
            "free_shipping": True,
            "exclusive_offers": True
        }
        customer_service_tools.service.get_membership_benefits.return_value = benefits_data

        result = await customer_service_tools.get_membership_benefits(123)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["membership"]["membership_level"] == "Gold"
        assert response_dict["data"]["membership"]["free_shipping"] is True

    @pytest.mark.asyncio
    async def test_get_membership_benefits_invalid_user_id(self, customer_service_tools):
        """测试无效用户ID的会员权益查询"""
        result = await customer_service_tools.get_membership_benefits(0)

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "用户ID必须是正整数" in response_dict["error"]


# ================================================================================================
# 场景3: 订单追踪与管理测试
# ================================================================================================

class TestOrderTrackingScenario:
    """测试场景3: 订单追踪与管理"""

    @pytest.mark.asyncio
    async def test_get_order_by_number_success(self, customer_service_tools, sample_order_data):
        """测试成功获取订单信息"""
        customer_service_tools.service.get_order_by_number.return_value = sample_order_data

        input_data = OrderQueryInput(order_number="ORD20241219001")
        result = await customer_service_tools.get_order_by_number(input_data)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["order"]["order_number"] == "ORD20241219001"
        assert response_dict["data"]["order"]["status"] == "shipped"

    @pytest.mark.asyncio
    async def test_get_order_by_number_not_found(self, customer_service_tools):
        """测试订单不存在"""
        customer_service_tools.service.get_order_by_number.return_value = None

        input_data = OrderQueryInput(order_number="NONEXISTENT")
        result = await customer_service_tools.get_order_by_number(input_data)

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "订单不存在或订单号错误" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_get_user_orders_success(self, customer_service_tools, sample_order_data):
        """测试成功获取用户订单列表"""
        orders_data = [sample_order_data]
        customer_service_tools.service.get_user_orders.return_value = orders_data

        result = await customer_service_tools.get_user_orders(123, limit=10, offset=0)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert len(response_dict["data"]["orders"]) == 1
        assert response_dict["data"]["pagination"]["limit"] == 10
        assert response_dict["data"]["pagination"]["offset"] == 0

    @pytest.mark.asyncio
    async def test_get_user_orders_invalid_params(self, customer_service_tools):
        """测试无效参数"""
        # 测试无效用户ID
        result = await customer_service_tools.get_user_orders(-1)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "用户ID必须是正整数" in response_dict["error"]

        # 测试无效limit
        result = await customer_service_tools.get_user_orders(123, limit=100)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "返回数量限制必须在1-50之间" in response_dict["error"]

        # 测试无效offset
        result = await customer_service_tools.get_user_orders(123, offset=-1)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "偏移量必须为非负整数" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_track_shipment_success(self, customer_service_tools):
        """测试成功追踪物流"""
        shipment_data = {
            "tracking_number": "SF1234567890",
            "status": "in_transit",
            "current_location": "北京分拣中心",
            "estimated_delivery": "2024-12-20T18:00:00",
            "tracking_history": [
                {"time": "2024-12-19T10:00:00", "location": "深圳发货中心", "status": "已发货"},
                {"time": "2024-12-19T15:00:00", "location": "广州中转站", "status": "运输中"}
            ]
        }
        customer_service_tools.service.track_shipment.return_value = shipment_data

        result = await customer_service_tools.track_shipment("SF1234567890")

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["shipment"]["status"] == "in_transit"
        assert response_dict["data"]["shipment"]["current_location"] == "北京分拣中心"

    @pytest.mark.asyncio
    async def test_track_shipment_invalid_number(self, customer_service_tools):
        """测试无效物流单号"""
        result = await customer_service_tools.track_shipment("")

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "物流单号不能为空" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_track_shipment_not_found(self, customer_service_tools):
        """测试物流信息不存在"""
        customer_service_tools.service.track_shipment.return_value = None

        result = await customer_service_tools.track_shipment("INVALID123")

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "物流信息不存在或单号错误" in response_dict["error"]


# ================================================================================================
# 场景4: 售后服务 - 退换货申请测试
# ================================================================================================

class TestRefundScenario:
    """测试场景4: 售后服务 - 退换货申请"""

    @pytest.mark.asyncio
    async def test_check_refund_eligibility_success(self, customer_service_tools):
        """测试成功检查退款资格"""
        eligibility_data = {
            "eligible": True,
            "reason": "订单在退款期限内",
            "refund_deadline": "2024-12-26T23:59:59",
            "refundable_amount": 7999.0
        }
        customer_service_tools.service.check_refund_eligibility.return_value = eligibility_data

        result = await customer_service_tools.check_refund_eligibility(123, user_id=456)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["eligibility"]["eligible"] is True
        assert response_dict["data"]["eligibility"]["refundable_amount"] == 7999.0

    @pytest.mark.asyncio
    async def test_check_refund_eligibility_invalid_params(self, customer_service_tools):
        """测试无效参数"""
        # 测试无效订单ID
        result = await customer_service_tools.check_refund_eligibility(-1)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "订单ID必须是正整数" in response_dict["error"]

        # 测试无效用户ID
        result = await customer_service_tools.check_refund_eligibility(123, user_id=-1)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "用户ID必须是正整数" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_create_refund_request_success(self, customer_service_tools):
        """测试成功创建退款申请"""
        refund_result = {
            "refund_id": 1,
            "refund_number": "REF20241219001",
            "status": "pending",
            "amount": 7999.0,
            "estimated_process_time": "3-5个工作日"
        }
        customer_service_tools.service.create_refund_request.return_value = refund_result

        input_data = RefundRequestInput(
            order_id=123,
            user_id=456,
            reason=RefundReason.QUALITY_ISSUE,
            amount=7999.0,
            description="商品有质量问题"
        )
        result = await customer_service_tools.create_refund_request(input_data)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["refund_request"]["refund_number"] == "REF20241219001"
        assert response_dict["data"]["refund_request"]["status"] == "pending"

    @pytest.mark.asyncio
    async def test_get_refund_status_success(self, customer_service_tools):
        """测试成功获取退款状态"""
        refund_status = {
            "refund_number": "REF20241219001",
            "status": "approved",
            "amount": 7999.0,
            "processed_at": "2024-12-19T16:00:00",
            "refund_method": "原路退回",
            "estimated_arrival": "2024-12-22T00:00:00"
        }
        customer_service_tools.service.get_refund_status.return_value = refund_status

        result = await customer_service_tools.get_refund_status("REF20241219001", user_id=456)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["refund"]["status"] == "approved"
        assert response_dict["data"]["refund"]["refund_method"] == "原路退回"

    @pytest.mark.asyncio
    async def test_get_refund_status_invalid_params(self, customer_service_tools):
        """测试无效参数"""
        # 测试空退款单号
        result = await customer_service_tools.get_refund_status("")
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "退款单号不能为空" in response_dict["error"]

        # 测试无效用户ID
        result = await customer_service_tools.get_refund_status("REF123", user_id=-1)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "用户ID必须是正整数" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_get_refund_status_not_found(self, customer_service_tools):
        """测试退款记录不存在"""
        customer_service_tools.service.get_refund_status.return_value = None

        result = await customer_service_tools.get_refund_status("NONEXISTENT")

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "退款记录不存在或单号错误" in response_dict["error"]


# ================================================================================================
# 场景5: 投诉与建议处理测试
# ================================================================================================

class TestComplaintScenario:
    """测试场景5: 投诉与建议处理"""

    @pytest.mark.asyncio
    async def test_create_complaint_success(self, customer_service_tools):
        """测试成功创建投诉"""
        complaint_result = {
            "complaint_id": 1,
            "complaint_number": "CMP20241219001",
            "status": "submitted",
            "estimated_response_time": "24小时内"
        }
        customer_service_tools.service.create_complaint.return_value = complaint_result

        input_data = ComplaintInput(
            user_id=123,
            category=ComplaintCategory.PRODUCT_QUALITY,
            subject="商品质量问题",
            description="收到的商品有明显瑕疵，要求退换货",
            order_id=456
        )
        result = await customer_service_tools.create_complaint(input_data)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["complaint"]["complaint_number"] == "CMP20241219001"
        assert response_dict["data"]["complaint"]["status"] == "submitted"

    @pytest.mark.asyncio
    async def test_get_complaint_status_success(self, customer_service_tools):
        """测试成功获取投诉状态"""
        complaint_status = {
            "complaint_number": "CMP20241219001",
            "status": "in_progress",
            "category": "product_quality",
            "subject": "商品质量问题",
            "created_at": "2024-12-19T10:00:00",
            "last_updated": "2024-12-19T14:00:00",
            "assigned_agent": "客服小王",
            "response": "我们已收到您的投诉，正在处理中"
        }
        customer_service_tools.service.get_complaint_status.return_value = complaint_status

        result = await customer_service_tools.get_complaint_status("CMP20241219001", user_id=123)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["complaint"]["status"] == "in_progress"
        assert response_dict["data"]["complaint"]["assigned_agent"] == "客服小王"

    @pytest.mark.asyncio
    async def test_get_complaint_status_invalid_params(self, customer_service_tools):
        """测试无效参数"""
        # 测试空投诉单号
        result = await customer_service_tools.get_complaint_status("")
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "投诉单号不能为空" in response_dict["error"]

        # 测试无效用户ID
        result = await customer_service_tools.get_complaint_status("CMP123", user_id=-1)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "用户ID必须是正整数" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_search_faq_success(self, customer_service_tools):
        """测试成功搜索FAQ"""
        faq_data = [
            {
                "id": 1,
                "question": "如何申请退货？",
                "answer": "您可以在订单详情页面点击申请退货按钮...",
                "category": "退货政策",
                "helpful_count": 150
            },
            {
                "id": 2,
                "question": "退货需要多长时间？",
                "answer": "一般情况下，退货处理需要3-5个工作日...",
                "category": "退货政策",
                "helpful_count": 120
            }
        ]
        customer_service_tools.service.search_faq.return_value = faq_data

        result = await customer_service_tools.search_faq("退货政策", category_id=1, limit=5)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert len(response_dict["data"]["faqs"]) == 2
        assert response_dict["data"]["search_query"] == "退货政策"
        assert response_dict["data"]["total_found"] == 2

    @pytest.mark.asyncio
    async def test_search_faq_invalid_params(self, customer_service_tools):
        """测试无效参数"""
        # 测试关键词太短
        result = await customer_service_tools.search_faq("a")
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "搜索关键词至少包含2个字符" in response_dict["error"]

        # 测试无效分类ID
        result = await customer_service_tools.search_faq("退货", category_id=-1)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "分类ID必须是正整数" in response_dict["error"]

        # 测试无效limit
        result = await customer_service_tools.search_faq("退货", limit=25)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "返回数量限制必须在1-20之间" in response_dict["error"]


# ================================================================================================
# 场景6: 人工客服服务转接测试
# ================================================================================================

class TestHumanAgentScenario:
    """测试场景6: 人工客服服务转接"""

    @pytest.mark.asyncio
    async def test_request_human_agent_success(self, customer_service_tools):
        """测试成功请求人工客服"""
        agent_result = {
            "request_id": 1,
            "status": "queued",
            "queue_position": 3,
            "estimated_wait_time": "5-10分钟",
            "message": "您已成功加入人工客服队列"
        }
        customer_service_tools.service.request_human_agent.return_value = agent_result

        input_data = HumanAgentRequestInput(
            user_id=123,
            conversation_id="conv_456",
            reason="复杂技术问题需要专业解答"
        )
        result = await customer_service_tools.request_human_agent(input_data)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["human_agent_request"]["status"] == "queued"
        assert response_dict["data"]["human_agent_request"]["queue_position"] == 3

    @pytest.mark.asyncio
    async def test_check_human_queue_status_success(self, customer_service_tools):
        """测试成功检查人工客服队列状态"""
        queue_status = {
            "conversation_id": "conv_456",
            "status": "waiting",
            "queue_position": 2,
            "estimated_wait_time": "3-5分钟",
            "agents_available": 2,
            "total_in_queue": 5
        }
        customer_service_tools.service.check_human_queue_status.return_value = queue_status

        result = await customer_service_tools.check_human_queue_status("conv_456", 123)

        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["queue_status"]["queue_position"] == 2
        assert response_dict["data"]["queue_status"]["agents_available"] == 2

    @pytest.mark.asyncio
    async def test_check_human_queue_status_invalid_params(self, customer_service_tools):
        """测试无效参数"""
        # 测试空对话ID
        result = await customer_service_tools.check_human_queue_status("", 123)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "对话ID不能为空" in response_dict["error"]

        # 测试无效用户ID
        result = await customer_service_tools.check_human_queue_status("conv_456", -1)
        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "用户ID必须是正整数" in response_dict["error"]


# ================================================================================================
# 工具定义接口测试
# ================================================================================================

class TestToolDefinitions:
    """测试工具定义接口"""

    def test_get_available_tools(self, customer_service_tools):
        """测试获取可用工具列表"""
        tools = customer_service_tools.get_available_tools()

        assert isinstance(tools, list)
        assert len(tools) > 0

        # 验证工具定义结构
        for tool in tools:
            assert "name" in tool
            assert "description" in tool
            assert "parameters" in tool
            assert isinstance(tool["parameters"], dict)
            assert "type" in tool["parameters"]
            assert "properties" in tool["parameters"]

        # 验证包含所有预期的工具
        tool_names = [tool["name"] for tool in tools]
        expected_tools = [
            "search_products",
            "get_product_details",
            "check_product_stock",
            "get_active_promotions",
            "get_user_coupons",
            "validate_coupon",
            "get_membership_benefits",
            "get_order_by_number",
            "get_user_orders",
            "track_shipment",
            "check_refund_eligibility",
            "create_refund_request",
            "get_refund_status",
            "create_complaint",
            "get_complaint_status",
            "search_faq",
            "request_human_agent",
            "check_human_queue_status"
        ]

        for expected_tool in expected_tools:
            assert expected_tool in tool_names, f"缺少工具: {expected_tool}"


# ================================================================================================
# 异常处理和边界条件测试
# ================================================================================================

class TestExceptionHandling:
    """测试异常处理和边界条件"""

    @pytest.mark.asyncio
    async def test_safe_execute_value_error(self, customer_service_tools):
        """测试_safe_execute处理ValueError"""
        async def failing_operation():
            raise ValueError("参数验证失败")

        result = await customer_service_tools._safe_execute(
            "test_tool",
            failing_operation,
            {"test": "params"}
        )

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "参数验证失败" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_safe_execute_general_exception(self, customer_service_tools):
        """测试_safe_execute处理一般异常"""
        async def failing_operation():
            raise Exception("系统内部错误")

        result = await customer_service_tools._safe_execute(
            "test_tool",
            failing_operation,
            {"test": "params"}
        )

        response_dict = json.loads(result)
        assert response_dict["success"] is False
        assert "系统错误" in response_dict["error"]

    @pytest.mark.asyncio
    async def test_safe_execute_different_return_types(self, customer_service_tools):
        """测试_safe_execute处理不同返回类型"""
        # 测试字典返回
        async def dict_operation():
            return {"key": "value"}

        result = await customer_service_tools._safe_execute("test", dict_operation, {})
        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["key"] == "value"

        # 测试列表返回
        async def list_operation():
            return [1, 2, 3]

        result = await customer_service_tools._safe_execute("test", list_operation, {})
        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["items"] == [1, 2, 3]
        assert response_dict["data"]["count"] == 3

        # 测试其他类型返回
        async def string_operation():
            return "test result"

        result = await customer_service_tools._safe_execute("test", string_operation, {})
        response_dict = json.loads(result)
        assert response_dict["success"] is True
        assert response_dict["data"]["result"] == "test result"
