# YUE商城客服工具测试文档

## 📋 概述

本目录包含对 `CustomerServiceTools` 类的全面测试，确保所有客服工具功能的正确性、稳定性和可靠性。

## 🧪 测试结构

### 测试文件组织
```
tests/
├── __init__.py                     # 测试包初始化
├── test_customer_service_tools.py  # 主要测试文件
├── README.md                       # 测试文档 (本文件)
└── conftest.py                     # 共享测试配置 (可选)
```

### 测试类结构
```python
# 辅助功能测试
TestCustomerServiceToolsHelpers    # 辅助方法测试
TestInputModels                     # 输入模型验证测试

# 六大核心场景测试
TestProductInformationScenario     # 场景1: 产品信息查询
TestPromotionScenario              # 场景2: 活动与优惠查询
TestOrderTrackingScenario          # 场景3: 订单追踪与管理
TestRefundScenario                 # 场景4: 退换货申请
TestComplaintScenario              # 场景5: 投诉与建议处理
TestHumanAgentScenario             # 场景6: 人工客服转接

# 系统功能测试
TestToolDefinitions                # 工具定义接口测试
TestExceptionHandling              # 异常处理测试
```

## 🚀 快速开始

### 1. 安装测试依赖
```bash
# 安装基础测试依赖
pip install pytest pytest-asyncio

# 或安装完整测试依赖
pip install -r requirements-test.txt
```

### 2. 运行测试

#### 使用测试脚本 (推荐)
```bash
# 运行所有测试
python run_tests.py

# 运行特定场景测试
python run_tests.py --scenario=1    # 产品信息查询
python run_tests.py --scenario=2    # 活动与优惠查询
python run_tests.py --scenario=3    # 订单追踪与管理
python run_tests.py --scenario=4    # 退换货申请
python run_tests.py --scenario=5    # 投诉与建议处理
python run_tests.py --scenario=6    # 人工客服转接

# 运行特定类型测试
python run_tests.py --helpers       # 辅助方法测试
python run_tests.py --models        # 输入模型测试
python run_tests.py --exceptions    # 异常处理测试

# 生成覆盖率报告
python run_tests.py --coverage

# 生成HTML测试报告
python run_tests.py --report

# 显示测试概览
python run_tests.py --summary
```

#### 直接使用pytest
```bash
# 运行所有测试
pytest tests/test_customer_service_tools.py -v

# 运行特定测试类
pytest tests/test_customer_service_tools.py::TestProductInformationScenario -v

# 运行特定测试方法
pytest tests/test_customer_service_tools.py::TestProductInformationScenario::test_search_products_success -v

# 生成覆盖率报告
pytest tests/test_customer_service_tools.py --cov=app.tools.customer_service_tools --cov-report=html
```

## 📊 测试覆盖范围

### 功能覆盖
- ✅ **18个客服工具方法** - 完整覆盖所有工具功能
- ✅ **6大核心场景** - 覆盖所有业务场景
- ✅ **输入验证** - 测试所有参数验证逻辑
- ✅ **错误处理** - 测试异常情况和边界条件
- ✅ **响应格式** - 验证统一的JSON响应格式
- ✅ **日志记录** - 测试日志功能的正确性

### 测试类型
- 🔧 **单元测试** - 测试单个方法的功能
- 🔗 **集成测试** - 测试方法间的协作
- ⚠️ **异常测试** - 测试错误处理机制
- 🎯 **边界测试** - 测试极限条件和边界值

### 测试数据
- 📝 **Mock数据** - 使用模拟数据进行测试
- 🎭 **测试夹具** - 提供可重用的测试数据
- 🔄 **动态生成** - 根据测试需要生成数据

## 🎯 测试重点

### 1. 参数验证测试
```python
# 测试有效参数
def test_valid_input():
    input_data = ProductSearchInput(query="iPhone", limit=10)
    assert input_data.query == "iPhone"

# 测试无效参数
def test_invalid_input():
    with pytest.raises(ValueError):
        ProductSearchInput(min_price=1000, max_price=500)
```

### 2. 响应格式测试
```python
def test_response_format():
    result = await tools.search_products(input_data)
    response_dict = json.loads(result)
    
    assert "success" in response_dict
    assert "data" in response_dict
    assert "timestamp" in response_dict
```

### 3. 异常处理测试
```python
def test_exception_handling():
    # 模拟服务异常
    service.search_products.side_effect = Exception("Database error")
    
    result = await tools.search_products(input_data)
    response_dict = json.loads(result)
    
    assert response_dict["success"] is False
    assert "系统错误" in response_dict["error"]
```

## 📈 测试报告

### 覆盖率报告
运行 `python run_tests.py --coverage` 后，可在以下位置查看覆盖率报告：
- **HTML报告**: `htmlcov/index.html`
- **终端报告**: 直接在命令行显示
- **XML报告**: `coverage.xml` (用于CI/CD)

### 测试报告
运行 `python run_tests.py --report` 后，可在以下位置查看测试报告：
- **HTML报告**: `reports/test_report.html`

## 🔧 测试配置

### pytest.ini
```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
addopts = -v --tb=short --strict-markers
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试
asyncio_mode = auto
```

### 环境变量
```bash
# 设置测试环境
export TESTING=true
export LOG_LEVEL=DEBUG
```

## 🚨 常见问题

### Q: 测试运行失败怎么办？
A: 
1. 检查是否安装了所有依赖: `pip install -r requirements-test.txt`
2. 确保数据库连接正常
3. 检查测试文件路径是否正确

### Q: 如何调试特定测试？
A:
```bash
# 运行单个测试并显示详细输出
pytest tests/test_customer_service_tools.py::test_method_name -v -s

# 使用pdb调试
pytest tests/test_customer_service_tools.py::test_method_name --pdb
```

### Q: 如何添加新的测试？
A:
1. 在相应的测试类中添加新的测试方法
2. 使用 `@pytest.mark.asyncio` 装饰异步测试
3. 遵循现有的命名约定: `test_功能_场景`

## 📝 最佳实践

### 1. 测试命名
- 使用描述性的测试方法名
- 格式: `test_功能_场景` (如: `test_search_products_success`)

### 2. 测试结构
- 使用AAA模式: Arrange, Act, Assert
- 每个测试只验证一个功能点
- 使用有意义的断言消息

### 3. Mock使用
- 只Mock外部依赖
- 保持Mock的简单性
- 验证Mock的调用参数

### 4. 异步测试
- 使用 `@pytest.mark.asyncio` 装饰器
- 正确处理异步上下文
- 避免异步测试中的竞态条件

## 🔄 持续集成

测试可以集成到CI/CD流水线中：

```yaml
# GitHub Actions 示例
- name: Run Tests
  run: |
    pip install -r requirements-test.txt
    python run_tests.py --coverage --report
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage.xml
```

## 📞 支持

如有测试相关问题，请联系：
- 📧 Email: <EMAIL>
- 📱 微信群: YUE开发者交流群
- 🐛 Issue: 在项目仓库提交Issue
