#!/usr/bin/env python3
"""
测试数据库连接和产品查询
"""
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from app.db.database import AsyncSessionLocal
from app.models.product import Product
from app.services.customer_service import CustomerServiceService


async def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        async with AsyncSessionLocal() as session:
            # 测试基本连接
            result = await session.execute(select(Product))
            products = result.scalars().all()
            print(f"✅ 数据库连接成功！找到 {len(products)} 个产品")
            
            # 显示产品信息
            for product in products:
                print(f"  📱 {product.name} - ¥{product.price} (库存: {product.stock_quantity})")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False


async def test_product_search():
    """测试产品搜索功能"""
    print("\n🔍 测试产品搜索功能...")
    
    try:
        async with AsyncSessionLocal() as session:
            service = CustomerServiceService(session)
            
            # 测试1: 搜索iPhone
            print("\n测试1: 搜索 'iPhone'")
            products = await service.search_products(query="iPhone")
            print(f"找到 {len(products)} 个iPhone产品:")
            for product in products:
                print(f"  📱 {product['name']} - ¥{product['price']}")
            
            # 测试2: 搜索所有产品
            print("\n测试2: 搜索所有产品")
            products = await service.search_products()
            print(f"找到 {len(products)} 个产品:")
            for product in products:
                print(f"  📱 {product['name']} - ¥{product['price']}")
            
            # 测试3: 按价格范围搜索
            print("\n测试3: 搜索价格在5000-10000之间的产品")
            products = await service.search_products(min_price=5000, max_price=10000)
            print(f"找到 {len(products)} 个产品:")
            for product in products:
                print(f"  📱 {product['name']} - ¥{product['price']}")
                
    except Exception as e:
        print(f"❌ 产品搜索测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("🚀 开始测试数据库和产品搜索功能\n")
    
    # 测试数据库连接
    db_ok = await test_database_connection()
    
    if db_ok:
        # 测试产品搜索
        await test_product_search()
    
    print("\n✅ 测试完成")


if __name__ == "__main__":
    asyncio.run(main())
