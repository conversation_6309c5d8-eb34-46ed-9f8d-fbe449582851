#!/usr/bin/env python3
"""
LLM配置测试脚本
验证LLM配置是否正确，并测试模型连接
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings


def test_config_values():
    """测试配置值"""
    print("🔧 测试LLM配置值...")
    print("=" * 50)
    
    print("🤖 OpenAI配置:")
    print(f"  API密钥: {'已设置' if settings.OPENAI_API_KEY else '未设置'}")
    if settings.OPENAI_API_KEY:
        print(f"    长度: {len(settings.OPENAI_API_KEY)} 字符")
        print(f"    前缀: {settings.OPENAI_API_KEY[:10]}...")
    
    print(f"  API基础URL: {settings.OPENAI_API_BASE or '使用默认'}")
    print(f"  模型: {settings.LLM_MODEL}")
    
    print("\n🔧 VLLM配置:")
    print(f"  API URL: {settings.VLLM_API_URL or '未设置'}")
    print(f"  API密钥: {'已设置' if settings.VLLM_API_KEY else '未设置'}")
    print(f"  模型: {settings.VLLM_API_MODEL}")
    
    return bool(settings.OPENAI_API_KEY)


def test_llm_import():
    """测试LLM模块导入"""
    print("\n📦 测试LLM模块导入...")
    
    try:
        from app.core.llms import _setup_model_client, _setup_vllm_model_client
        print("✅ LLM模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ LLM模块导入失败: {e}")
        print("💡 可能需要安装autogen_ext依赖:")
        print("   pip install autogen-ext[openai]")
        return False


def test_model_client_setup():
    """测试模型客户端设置"""
    print("\n🔧 测试模型客户端设置...")
    
    try:
        from app.core.llms import _setup_model_client, _setup_vllm_model_client
        
        # 测试OpenAI客户端
        if settings.OPENAI_API_KEY:
            try:
                client = _setup_model_client()
                print("✅ OpenAI客户端设置成功")
                print(f"  模型: {settings.LLM_MODEL}")
                print(f"  基础URL: {settings.OPENAI_API_BASE or '默认'}")
                return True
            except Exception as e:
                print(f"❌ OpenAI客户端设置失败: {e}")
                return False
        else:
            print("⚠️ 跳过OpenAI客户端测试（未设置API密钥）")
            return False
            
    except ImportError:
        print("❌ 无法导入LLM模块")
        return False


async def test_model_connection():
    """测试模型连接（如果可能）"""
    print("\n🌐 测试模型连接...")
    
    if not settings.OPENAI_API_KEY:
        print("⚠️ 跳过连接测试（未设置API密钥）")
        return False
    
    try:
        from app.core.llms import model_client
        
        # 简单的测试消息
        test_messages = [
            {"role": "user", "content": "Hello, this is a test message. Please respond with 'Test successful'."}
        ]
        
        print("📡 发送测试消息...")
        print(f"  模型: {settings.LLM_MODEL}")
        print(f"  API: {settings.OPENAI_API_BASE or 'OpenAI默认'}")
        
        # 注意：这里只是设置客户端，实际调用可能需要更多配置
        print("✅ 客户端配置完成")
        print("💡 实际API调用需要在具体使用时进行")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型连接测试失败: {e}")
        return False


def show_usage_examples():
    """显示使用示例"""
    print("\n📚 使用示例:")
    print("=" * 50)
    
    print("1. 在代码中使用LLM客户端:")
    print("""
from app.core.llms import model_client

# 使用模型客户端
messages = [{"role": "user", "content": "Hello"}]
response = await model_client.create(messages)
""")
    
    print("2. 在客服工具中集成:")
    print("""
from app.core.config import settings
from app.core.llms import model_client

# 客服场景处理
async def handle_customer_query(query: str):
    messages = [
        {"role": "system", "content": f"你是{settings.CUSTOMER_SERVICE_NAME}，一个专业的客服助手。"},
        {"role": "user", "content": query}
    ]
    response = await model_client.create(messages)
    return response
""")
    
    print("3. 配置不同的模型:")
    print("""
# 在.env文件中设置:
LLM_MODEL=gpt-4
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=your-api-key

# 或者使用其他兼容的API:
LLM_MODEL=deepseek-chat
OPENAI_API_BASE=https://api.deepseek.com/v1
OPENAI_API_KEY=your-deepseek-key
""")


def main():
    """主函数"""
    print("🧪 YUE商城客服系统 - LLM配置测试")
    print("=" * 60)
    
    # 显示当前配置文件位置
    env_file = ".env"
    if os.path.exists(env_file):
        print(f"📁 配置文件: {os.path.abspath(env_file)}")
    else:
        print(f"⚠️ 未找到配置文件: {env_file}")
    
    print(f"🐍 Python路径: {sys.path[0]}")
    
    # 运行测试
    tests = [
        ("配置值检查", test_config_values),
        ("模块导入测试", test_llm_import),
        ("客户端设置测试", test_model_client_setup),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append(False)
    
    # 异步测试
    print(f"\n{'='*20} 连接测试 {'='*20}")
    try:
        connection_result = asyncio.run(test_model_connection())
        results.append(connection_result)
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        results.append(False)
    
    # 显示使用示例
    show_usage_examples()
    
    # 总结
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！LLM配置正确。")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
        
        if not settings.OPENAI_API_KEY:
            print("\n💡 建议:")
            print("  1. 在.env文件中设置OPENAI_API_KEY")
            print("  2. 如果使用其他API，设置相应的OPENAI_API_BASE")
            print("  3. 确保LLM_MODEL设置正确")
    
    print(f"\n🚀 启动服务器: python start_server.py")
    print(f"📖 API文档: http://localhost:{settings.PORT}/docs")


if __name__ == "__main__":
    main()
