# YUE商城智能客服系统 - 后端

## 📁 项目结构

```
backend/
├── 📁 app/                          # 核心应用代码
│   ├── 📁 api/                      # API路由层
│   ├── 📁 core/                     # 核心配置和工具
│   ├── 📁 db/                       # 数据库层
│   ├── 📁 models/                   # 数据模型
│   ├── 📁 services/                 # 业务逻辑层
│   ├── 📁 tools/                    # 工具和集成
│   ├── 📁 utils/                    # 通用工具函数
│   └── main.py                      # 应用入口
├── 📁 tests/                        # 测试代码
├── 📁 scripts/                      # 脚本文件
├── 📁 docs/                         # 文档
├── 📁 config/                       # 配置文件
├── 📁 data/                         # 数据文件
├── 📁 logs/                         # 日志文件
├── 📁 temp/                         # 临时文件和测试文件
├── requirements.txt                 # 生产依赖
└── requirements-test.txt            # 测试依赖
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 复制配置文件
cp config/.env.example config/.env
# 编辑 config/.env 文件，设置必要的配置
```

### 2. 数据库初始化
```bash
# 快速启动（包含测试数据）
python scripts/quick_start.py

# 或者单独初始化数据库
python scripts/init_database.py
```

### 3. 启动服务
```bash
# 使用启动脚本
python scripts/start_server.py

# 或者直接启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 4. 访问服务
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/

## 🧪 测试

```bash
# 运行所有测试
python scripts/run_tests.py

# 运行特定场景测试
python scripts/run_tests.py --scenario=1

# 生成覆盖率报告
python scripts/run_tests.py --coverage

# 查看测试概览
python scripts/run_tests.py --summary
```

## 📚 文档

- [配置指南](docs/CONFIG_GUIDE.md)
- [SQLite设置指南](docs/SQLITE_SETUP_GUIDE.md)
- [客服系统文档](docs/CUSTOMER_SERVICE_README.md)
- [API文档](docs/README.md)

## 🛠️ 开发

### 目录说明

- **app/**: 核心应用代码，包含API、业务逻辑、数据模型等
- **tests/**: 测试代码，包含单元测试和集成测试
- **scripts/**: 可执行脚本，包含启动、初始化、测试等脚本
- **docs/**: 项目文档和指南
- **config/**: 配置文件，包含环境变量和应用配置
- **data/**: 数据文件，包含数据库文件和静态数据
- **logs/**: 日志文件目录
- **temp/**: 临时文件和演示脚本

### 技术栈

- **框架**: FastAPI
- **数据库**: SQLite (支持 MySQL)
- **ORM**: SQLAlchemy (异步)
- **AI**: OpenAI API (支持多种提供商)
- **测试**: pytest + pytest-asyncio

## 🔧 配置

主要配置文件位于 `config/.env`，包含：

- API配置 (CORS、端口等)
- 数据库配置
- AI模型配置 (OpenAI、DeepSeek等)
- 日志配置
- 客服系统配置

详细配置说明请参考 [配置指南](docs/CONFIG_GUIDE.md)。

## 📝 更新日志

### v0.1.0 (当前版本)
- ✅ 完成目录结构重新整理
- ✅ 实现6大客服场景功能
- ✅ 完善测试覆盖
- ✅ 优化配置管理
- ✅ 完善文档体系
