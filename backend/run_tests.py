#!/usr/bin/env python3
"""
YUE商城客服工具测试运行脚本

本脚本提供多种测试运行选项：
- 运行所有测试
- 运行特定场景测试
- 生成测试报告
- 代码覆盖率分析

使用方法:
    python run_tests.py                    # 运行所有测试
    python run_tests.py --scenario=1       # 运行场景1测试
    python run_tests.py --coverage         # 运行测试并生成覆盖率报告
    python run_tests.py --report           # 生成详细测试报告
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def run_command(cmd, description=""):
    """运行命令并处理结果"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"执行命令: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=project_root)
        
        if result.stdout:
            print("📋 输出:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ 警告/错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} 完成")
        else:
            print(f"❌ {description} 失败 (退出码: {result.returncode})")
            
        return result.returncode == 0
        
    except FileNotFoundError:
        print(f"❌ 命令未找到: {cmd[0]}")
        print("请确保已安装pytest: pip install pytest pytest-asyncio")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False


def install_dependencies():
    """安装测试依赖"""
    dependencies = [
        "pytest>=6.0.0",
        "pytest-asyncio>=0.21.0", 
        "pytest-cov>=4.0.0",
        "pytest-html>=3.1.0",
        "pytest-mock>=3.10.0"
    ]
    
    cmd = [sys.executable, "-m", "pip", "install"] + dependencies
    return run_command(cmd, "安装测试依赖")


def run_all_tests(coverage=False, report=False):
    """运行所有测试"""
    cmd = [sys.executable, "-m", "pytest", "tests/test_customer_service_tools.py"]
    
    if coverage:
        cmd.extend([
            "--cov=app.tools.customer_service_tools",
            "--cov-report=html:htmlcov",
            "--cov-report=term-missing",
            "--cov-report=xml"
        ])
    
    if report:
        cmd.extend([
            "--html=reports/test_report.html",
            "--self-contained-html"
        ])
    
    # 创建报告目录
    if report:
        os.makedirs("reports", exist_ok=True)
    
    return run_command(cmd, "运行所有客服工具测试")


def run_scenario_tests(scenario_num):
    """运行特定场景的测试"""
    scenario_map = {
        1: "TestProductInformationScenario",
        2: "TestPromotionScenario", 
        3: "TestOrderTrackingScenario",
        4: "TestRefundScenario",
        5: "TestComplaintScenario",
        6: "TestHumanAgentScenario"
    }
    
    if scenario_num not in scenario_map:
        print(f"❌ 无效的场景编号: {scenario_num}")
        print("可用场景: 1-6")
        return False
    
    test_class = scenario_map[scenario_num]
    cmd = [
        sys.executable, "-m", "pytest", 
        f"tests/test_customer_service_tools.py::{test_class}",
        "-v"
    ]
    
    return run_command(cmd, f"运行场景{scenario_num}测试 ({test_class})")


def run_specific_tests(test_names):
    """运行特定的测试方法"""
    for test_name in test_names:
        cmd = [
            sys.executable, "-m", "pytest",
            f"tests/test_customer_service_tools.py::{test_name}",
            "-v", "-s"
        ]
        
        success = run_command(cmd, f"运行测试: {test_name}")
        if not success:
            return False
    
    return True


def run_input_model_tests():
    """运行输入模型验证测试"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_customer_service_tools.py::TestInputModels",
        "-v"
    ]
    
    return run_command(cmd, "运行输入模型验证测试")


def run_helper_tests():
    """运行辅助方法测试"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_customer_service_tools.py::TestCustomerServiceToolsHelpers",
        "-v"
    ]
    
    return run_command(cmd, "运行辅助方法测试")


def run_exception_tests():
    """运行异常处理测试"""
    cmd = [
        sys.executable, "-m", "pytest",
        "tests/test_customer_service_tools.py::TestExceptionHandling",
        "-v"
    ]
    
    return run_command(cmd, "运行异常处理测试")


def show_test_summary():
    """显示测试概览"""
    print(f"\n{'='*60}")
    print("📊 YUE商城客服工具测试概览")
    print(f"{'='*60}")
    print("""
🧪 测试覆盖范围:
   ├── 📝 输入模型验证测试 (TestInputModels)
   ├── 🔧 辅助方法测试 (TestCustomerServiceToolsHelpers)
   ├── 🔍 场景1: 产品信息查询 (TestProductInformationScenario)
   ├── 🎉 场景2: 活动与优惠查询 (TestPromotionScenario)
   ├── 📦 场景3: 订单追踪与管理 (TestOrderTrackingScenario)
   ├── 🔄 场景4: 退换货申请 (TestRefundScenario)
   ├── 📞 场景5: 投诉与建议处理 (TestComplaintScenario)
   ├── 👥 场景6: 人工客服转接 (TestHumanAgentScenario)
   ├── 📋 工具定义接口测试 (TestToolDefinitions)
   └── ⚠️ 异常处理测试 (TestExceptionHandling)

📈 测试统计:
   • 总测试类: 10个
   • 总测试方法: 60+个
   • 覆盖工具: 18个客服工具
   • 测试类型: 单元测试 + 集成测试 + 异常测试

🎯 测试目标:
   ✅ 验证所有工具方法的正确性
   ✅ 确保参数验证的有效性
   ✅ 测试错误处理的完整性
   ✅ 验证响应格式的一致性
   ✅ 确保日志记录的准确性
""")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="YUE商城客服工具测试运行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_tests.py                    # 运行所有测试
  python run_tests.py --scenario=1       # 运行场景1测试
  python run_tests.py --coverage         # 运行测试并生成覆盖率报告
  python run_tests.py --report           # 生成HTML测试报告
  python run_tests.py --install          # 安装测试依赖
  python run_tests.py --helpers          # 运行辅助方法测试
  python run_tests.py --models           # 运行输入模型测试
  python run_tests.py --exceptions       # 运行异常处理测试
        """
    )
    
    parser.add_argument("--scenario", type=int, choices=[1,2,3,4,5,6], 
                       help="运行特定场景的测试 (1-6)")
    parser.add_argument("--coverage", action="store_true", 
                       help="生成代码覆盖率报告")
    parser.add_argument("--report", action="store_true", 
                       help="生成HTML测试报告")
    parser.add_argument("--install", action="store_true", 
                       help="安装测试依赖")
    parser.add_argument("--helpers", action="store_true", 
                       help="运行辅助方法测试")
    parser.add_argument("--models", action="store_true", 
                       help="运行输入模型验证测试")
    parser.add_argument("--exceptions", action="store_true", 
                       help="运行异常处理测试")
    parser.add_argument("--summary", action="store_true", 
                       help="显示测试概览")
    
    args = parser.parse_args()
    
    # 显示概览
    if args.summary:
        show_test_summary()
        return
    
    # 安装依赖
    if args.install:
        success = install_dependencies()
        if success:
            print("\n✅ 测试依赖安装完成！")
        else:
            print("\n❌ 测试依赖安装失败！")
        return
    
    # 检查测试文件是否存在
    test_file = project_root / "tests" / "test_customer_service_tools.py"
    if not test_file.exists():
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print("🧪 YUE商城客服工具测试运行器")
    print(f"📁 项目路径: {project_root}")
    print(f"📄 测试文件: {test_file}")
    
    success = True
    
    # 运行特定场景测试
    if args.scenario:
        success = run_scenario_tests(args.scenario)
    
    # 运行辅助方法测试
    elif args.helpers:
        success = run_helper_tests()
    
    # 运行输入模型测试
    elif args.models:
        success = run_input_model_tests()
    
    # 运行异常处理测试
    elif args.exceptions:
        success = run_exception_tests()
    
    # 运行所有测试
    else:
        success = run_all_tests(coverage=args.coverage, report=args.report)
    
    # 显示结果
    print(f"\n{'='*60}")
    if success:
        print("🎉 测试运行完成！")
        if args.coverage:
            print("📊 覆盖率报告已生成: htmlcov/index.html")
        if args.report:
            print("📋 测试报告已生成: reports/test_report.html")
    else:
        print("❌ 测试运行失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
