# YUE商城客服系统 - SQLite版本设置指南

## 🎯 概述

本指南将帮助您快速设置和运行基于SQLite的YUE商城智能客服系统。SQLite版本无需额外的数据库服务器，开箱即用，非常适合开发、测试和演示。

## 🚀 快速开始（3分钟搞定）

### 1. 克隆项目并安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 一键初始化

```bash
python quick_start.py
```

### 3. 启动服务

```bash
uvicorn app.main:app --reload
```

### 4. 访问API文档

打开浏览器访问：http://localhost:8000/docs

## 📊 数据库特性

### SQLite优势
- ✅ **零配置**：无需安装数据库服务器
- ✅ **轻量级**：单文件数据库，易于备份和迁移
- ✅ **高性能**：对于中小型应用性能优异
- ✅ **跨平台**：支持Windows、macOS、Linux
- ✅ **ACID兼容**：支持事务和并发

### 数据库文件
- **位置**：`./yue_platform.db`
- **大小**：约200-500KB（包含测试数据）
- **备份**：直接复制文件即可

## 🧪 测试数据详情

### 完整测试数据集
运行`quick_start.py`后，系统将创建：

#### 👥 用户数据（4个）
```
张三 (ID:1) - 银卡会员，积分1500，已购买iPhone
李四 (ID:2) - 普通会员，积分450，有未使用优惠券
王五 (ID:3) - 活跃用户，登录42次
赵六 (ID:4) - 新用户，未验证
```

#### 📦 产品数据（6个）
```
1. iPhone 15 Pro (¥7999) - 库存50，评分4.8
2. MacBook Air M2 (¥8999) - 库存30，评分4.9
3. 小米13 Ultra (¥5999) - 库存80，评分4.7
4. 优衣库羽绒服 (¥399) - 库存120，热销
5. 戴森V15吸尘器 (¥4990) - 库存25，高端
6. 更多产品...
```

#### 🛒 订单数据（3个）
```
YUE20231201001 - 张三的iPhone订单，已完成，¥7799
YUE20231202001 - 李四的MacBook订单，配送中，¥8999
YUE20231203001 - 张三的小米订单，处理中，¥5914
```

#### 🚚 物流数据（2个）
```
SF1234567890 - 顺丰快递，已签收，完整跟踪记录
YTO9876543210 - 圆通快递，运输中，实时状态
```

#### 🎫 优惠券数据（3张）
```
WELCOME200_USER2 - 李四的200元新用户券，未使用
BIRTHDAY100_USER1 - 张三的100元生日券，可用
DOUBLE11 - 双11促销码，全场20%折扣
```

#### ❓ FAQ数据（12个）
```
订单相关：查询状态、支付方式、取消订单
售后服务：退货政策、申请退款、质量问题
账户管理：注册账户、找回密码、会员权益
产品咨询：选择产品、保修政策
```

#### 📞 客服数据（2个）
```
客服小王 - 在线，最大5个并发对话
客服小李 - 在线，最大8个并发对话，高级专员
```

#### 📝 投诉数据（2个）
```
CP20231201000001 - 物流延迟投诉，已解决，满意度4分
CP20231202000001 - 产品质量投诉，处理中，高优先级
```

## 🔧 功能测试

### 自动化测试
```bash
python test_sqlite.py
```

测试内容：
- ✅ 数据库连接
- ✅ 数据完整性
- ✅ 客服功能
- ✅ 工具集成

### 手动测试场景

#### 场景1：产品搜索
```bash
curl "http://localhost:8000/api/customer-service/products/search?query=iPhone"
```

#### 场景2：订单查询
```bash
curl "http://localhost:8000/api/customer-service/orders/YUE20231201001"
```

#### 场景3：物流追踪
```bash
curl "http://localhost:8000/api/customer-service/shipments/SF1234567890"
```

#### 场景4：FAQ搜索
```bash
curl "http://localhost:8000/api/customer-service/faq/search?query=退货"
```

#### 场景5：优惠券验证
```bash
curl -X POST "http://localhost:8000/api/customer-service/coupons/validate" \
  -H "Content-Type: application/json" \
  -d '{"code":"WELCOME200_USER2","user_id":2,"order_amount":1000}'
```

## 🛠️ 开发指南

### 数据库操作
```python
# 连接数据库
from app.db.database import AsyncSessionLocal

async with AsyncSessionLocal() as db:
    # 执行数据库操作
    pass
```

### 添加测试数据
```python
# 在 app/db/init_db.py 中添加
new_product = Product(
    name="新产品",
    sku="NEW001",
    price=999.00,
    # ... 其他字段
)
session.add(new_product)
await session.commit()
```

### 重置数据库
```bash
# 删除数据库文件
rm yue_platform.db

# 重新初始化
python quick_start.py
```

## 📈 性能优化

### SQLite配置优化
```python
# 在 app/db/database.py 中
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=False,  # 生产环境关闭SQL日志
    pool_pre_ping=True,
    connect_args={
        "check_same_thread": False,
        "timeout": 20
    }
)
```

### 查询优化
- 使用索引加速查询
- 合理使用分页
- 避免N+1查询问题
- 使用selectinload预加载关联数据

## 🔄 迁移到其他数据库

如需迁移到MySQL或PostgreSQL：

### 1. 修改配置
```python
# app/core/config.py
DATABASE_URL = "mysql+aiomysql://user:pass@localhost/db"
# 或
DATABASE_URL = "postgresql+asyncpg://user:pass@localhost/db"
```

### 2. 安装对应驱动
```bash
# MySQL
pip install aiomysql

# PostgreSQL
pip install asyncpg
```

### 3. 数据迁移
```bash
# 导出SQLite数据
sqlite3 yue_platform.db .dump > backup.sql

# 导入到新数据库（需要格式转换）
```

## 🐛 常见问题

### Q: 数据库文件损坏怎么办？
A: 删除`yue_platform.db`文件，重新运行`python quick_start.py`

### Q: 如何备份数据？
A: 直接复制`yue_platform.db`文件即可

### Q: 支持并发访问吗？
A: SQLite支持多读单写，适合中小型应用

### Q: 如何查看数据库内容？
A: 使用SQLite浏览器工具，如DB Browser for SQLite

### Q: 性能如何？
A: 对于客服系统的典型负载，SQLite性能完全够用

## 📞 技术支持

如遇到问题：
1. 查看日志文件
2. 运行测试脚本诊断
3. 检查数据库文件权限
4. 重新初始化数据库

## 🎉 总结

SQLite版本的YUE商城客服系统提供了：
- ✅ 完整的六大客服场景功能
- ✅ 丰富的测试数据
- ✅ 零配置快速启动
- ✅ 完善的API接口
- ✅ 工具化的AI集成

非常适合：
- 🔬 功能演示和测试
- 🚀 快速原型开发
- 📚 学习和研究
- 🏢 中小型部署

开始您的智能客服之旅吧！🚀
