# YUE商城客服系统 - 配置指南

## 📋 概述

本指南详细说明如何配置YUE商城智能客服系统，包括环境变量设置、LLM配置和系统参数调整。

## 🔧 配置文件结构

### 主要配置文件

1. **`.env`** - 环境变量配置文件
2. **`app/core/config.py`** - 配置类定义
3. **`app/core/llms.py`** - LLM客户端配置

### 配置加载流程

```
.env 文件 → config.py (Settings类) → llms.py (模型客户端) → 应用使用
```

## 📝 环境变量配置

### 完整的 `.env` 文件示例

```env
# API配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080,http://localhost:5173

# 数据库配置
DATABASE_URL=sqlite+aiosqlite:///./yue_platform.db

# OpenAI配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_BASE=https://api.openai.com/v1
LLM_MODEL=gpt-3.5-turbo

# 应用配置
PROJECT_NAME=YUE智能体平台
VERSION=0.1.0
DEBUG=false

# 服务器配置
HOST=0.0.0.0
PORT=8000

# JWT配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 客服配置
CUSTOMER_SERVICE_NAME=小yue
MAX_CONVERSATION_HISTORY=50

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

## 🤖 LLM配置详解

### OpenAI兼容API配置

#### 1. 使用OpenAI官方API
```env
OPENAI_API_KEY=sk-your-openai-key
OPENAI_API_BASE=https://api.openai.com/v1
LLM_MODEL=gpt-3.5-turbo
```

#### 2. 使用DeepSeek API
```env
OPENAI_API_KEY=sk-your-deepseek-key
OPENAI_API_BASE=https://api.deepseek.com/v1
LLM_MODEL=deepseek-chat
```

#### 3. 使用其他兼容API
```env
OPENAI_API_KEY=your-api-key
OPENAI_API_BASE=https://your-api-endpoint/v1
LLM_MODEL=your-model-name
```

### 支持的模型

| 提供商 | 模型名称 | API基础URL |
|--------|----------|------------|
| OpenAI | gpt-3.5-turbo, gpt-4 | https://api.openai.com/v1 |
| DeepSeek | deepseek-chat | https://api.deepseek.com/v1 |
| 智谱AI | glm-4 | https://open.bigmodel.cn/api/paas/v4 |
| 月之暗面 | moonshot-v1-8k | https://api.moonshot.cn/v1 |

## 🔍 配置验证

### 1. 基础配置测试
```bash
python test_config.py
```

### 2. LLM配置测试
```bash
python test_llm_config.py
```

### 3. 完整系统测试
```bash
python test_sqlite.py
```

## 🚀 启动方式

### 1. 使用配置启动
```bash
python start_server.py
```

### 2. 直接启动
```bash
python -m uvicorn app.main:app --reload
```

### 3. 使用环境变量覆盖
```bash
PORT=9000 DEBUG=true python start_server.py
```

## ⚙️ 高级配置

### 数据库配置

#### SQLite（默认）
```env
DATABASE_URL=sqlite+aiosqlite:///./yue_platform.db
```

#### MySQL
```env
DATABASE_URL=mysql+aiomysql://user:password@localhost:3306/yue_platform
```

#### PostgreSQL
```env
DATABASE_URL=postgresql+asyncpg://user:password@localhost:5432/yue_platform
```

### CORS配置

#### 单个域名
```env
BACKEND_CORS_ORIGINS=http://localhost:3000
```

#### 多个域名（逗号分隔）
```env
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8080
```

#### 允许所有域名（仅开发环境）
```env
BACKEND_CORS_ORIGINS=*
```

### 日志配置

#### 开发环境
```env
LOG_LEVEL=DEBUG
LOG_FILE=logs/debug.log
DEBUG=true
```

#### 生产环境
```env
LOG_LEVEL=INFO
LOG_FILE=/var/log/yue-platform/app.log
DEBUG=false
```

## 🔐 安全配置

### JWT密钥生成
```python
import secrets
secret_key = secrets.token_urlsafe(32)
print(f"SECRET_KEY={secret_key}")
```

### API密钥管理
- 使用环境变量存储敏感信息
- 不要将API密钥提交到版本控制
- 定期轮换API密钥
- 使用不同环境的不同密钥

## 🐛 常见问题

### Q1: 配置不生效怎么办？
A: 检查以下几点：
1. `.env` 文件是否在正确位置
2. 环境变量名称是否正确
3. 重启应用程序
4. 运行 `python test_config.py` 验证

### Q2: LLM连接失败怎么办？
A: 检查以下几点：
1. API密钥是否正确
2. API基础URL是否正确
3. 网络连接是否正常
4. 运行 `python test_llm_config.py` 诊断

### Q3: CORS错误怎么解决？
A: 确保前端域名在 `BACKEND_CORS_ORIGINS` 中：
```env
BACKEND_CORS_ORIGINS=http://localhost:3000,http://your-frontend-domain.com
```

### Q4: 数据库连接失败怎么办？
A: 检查数据库URL格式和权限：
```env
# SQLite - 确保目录可写
DATABASE_URL=sqlite+aiosqlite:///./yue_platform.db

# MySQL - 确保用户名密码正确
DATABASE_URL=mysql+aiomysql://user:pass@host:port/db
```

## 📚 配置示例

### 开发环境配置
```env
# 开发环境 .env
DEBUG=true
LOG_LEVEL=DEBUG
HOST=127.0.0.1
PORT=8000
DATABASE_URL=sqlite+aiosqlite:///./dev_yue_platform.db
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

### 生产环境配置
```env
# 生产环境 .env
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000
DATABASE_URL=mysql+aiomysql://user:pass@db-server:3306/yue_platform
BACKEND_CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com
SECRET_KEY=your-production-secret-key
```

### 测试环境配置
```env
# 测试环境 .env
DEBUG=false
LOG_LEVEL=WARNING
DATABASE_URL=sqlite+aiosqlite:///./test_yue_platform.db
BACKEND_CORS_ORIGINS=http://localhost:3000
```

## 🔄 配置更新

### 热重载配置
在开发模式下，修改 `.env` 文件后需要重启服务器。

### 配置验证
每次修改配置后，建议运行：
```bash
python test_config.py
python test_llm_config.py
```

### 配置备份
重要配置文件建议备份：
```bash
cp .env .env.backup
cp app/core/config.py app/core/config.py.backup
```

## 📞 技术支持

如果遇到配置问题：
1. 查看日志文件
2. 运行配置测试脚本
3. 检查环境变量设置
4. 参考本文档的常见问题部分

---

**注意**: 请妥善保管包含敏感信息的配置文件，不要将其提交到公共代码仓库。
