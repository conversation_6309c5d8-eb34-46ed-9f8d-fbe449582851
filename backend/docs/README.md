# Yue智能体平台 - 后端API实现指南

本目录包含Yue智能体平台的后端实现。后端使用Python + FastAPI框架，提供RESTful API服务。

## 技术栈

- **框架**: FastAPI
- **数据库**:
  - MySQL: 存储用户信息、会话历史、配置等
  - Milvus: 向量数据库，存储文档嵌入向量
- **文件存储**: Minio
- **依赖管理**: Poetry或pip with requirements.txt

## 后端API结构

```
backend/
├── app/                    # 主应用代码
│   ├── api/                # API路由
│   │   ├── __init__.py
│   │   ├── auth.py         # 认证相关API
│   │   ├── chat.py         # 对话相关API
│   │   ├── knowledge.py    # 知识库相关API
│   │   ├── sql.py          # Text2SQL相关API
│   │   └── content.py      # 文案创作相关API
│   ├── core/               # 核心功能
│   │   ├── __init__.py
│   │   ├── config.py       # 配置管理
│   │   ├── security.py     # 安全相关
│   │   └── logger.py       # 日志
│   ├── db/                 # 数据库
│   │   ├── __init__.py
│   │   ├── mysql.py        # MySQL连接
│   │   └── milvus.py       # Milvus连接
│   ├── models/             # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py         # 用户模型
│   │   ├── chat.py         # 对话模型
│   │   └── document.py     # 文档模型
│   ├── services/           # 业务逻辑
│   │   ├── __init__.py
│   │   ├── chat_service.py # 对话服务
│   │   ├── rag_service.py  # RAG服务
│   │   ├── sql_service.py  # SQL服务
│   │   └── content_service.py # 文案服务
│   ├── utils/              # 工具函数
│   │   ├── __init__.py
│   │   ├── vector.py       # 向量操作
│   │   └── file.py         # 文件操作
│   └── main.py             # 应用入口
├── tests/                  # 测试
├── .env.example            # 环境变量示例
├── requirements.txt        # 依赖
└── README.md               # 说明文档
```

## 主要API端点

以下是需要实现的主要API端点：

### 认证

- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息

### 智能客服

- `POST /api/chat/customer-service` - 创建客服对话
- `GET /api/chat/customer-service/history` - 获取客服对话历史

### Text2SQL

- `POST /api/sql/query` - 执行自然语言转SQL查询
- `GET /api/sql/history` - 获取SQL查询历史

### 知识库问答

- `POST /api/knowledge/upload` - 上传知识文档
- `GET /api/knowledge/documents` - 获取所有文档
- `DELETE /api/knowledge/documents/{id}` - 删除文档
- `POST /api/knowledge/chat` - 创建知识库对话

### 文案创作

- `GET /api/content/templates` - 获取文案模板
- `POST /api/content/generate` - 生成文案
- `GET /api/content/history` - 获取历史文案

## 数据流

1. **智能客服流程**:
   - 前端发送用户问题到 `/api/chat/customer-service`
   - 后端调用大语言模型处理问题
   - 使用流式响应将回答实时返回给前端

2. **Text2SQL流程**:
   - 前端发送自然语言问题到 `/api/sql/query`
   - 后端将其转换为SQL并执行
   - 返回SQL语句、解释和结果

3. **知识库问答流程**:
   - 用户上传文档到 `/api/knowledge/upload`
   - 后端处理文档并存储向量化结果
   - 用户问题发送到 `/api/knowledge/chat`
   - 后端使用RAG技术检索相关信息并生成回答

4. **文案创作流程**:
   - 前端获取模板列表 `/api/content/templates`
   - 用户填写模板并发送到 `/api/content/generate`
   - 后端生成文案并返回

## 开始实现

1. 设置环境:
   ```bash
   # 创建虚拟环境
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   venv\Scripts\activate  # Windows
   
   # 安装依赖
   pip install -r requirements.txt
   ```

2. 配置环境变量:
   - 复制 `.env.example` 为 `.env`
   - 填写必要的配置参数

3. 启动服务:
   ```bash
   uvicorn app.main:app --reload
   ```

## 数据库配置

### MySQL 表结构示例

```sql
-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 对话历史表
CREATE TABLE chat_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    chat_type VARCHAR(20), -- 'customer-service', 'knowledge-base'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 消息表
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chat_id INT,
    role VARCHAR(10), -- 'user', 'assistant'
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chat_id) REFERENCES chat_history(id)
);

-- 文档表
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'processing', -- 'processing', 'ready'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- SQL查询历史表
CREATE TABLE sql_queries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    natural_query TEXT,
    sql_query TEXT,
    result JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 文案历史表
CREATE TABLE content_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    template_id VARCHAR(50),
    title VARCHAR(255),
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### Milvus 配置

对于文档的向量存储，建议使用Milvus创建如下集合:

```python
from pymilvus import Collection, FieldSchema, CollectionSchema, DataType

# 定义字段
id_field = FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True)
content_field = FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535)
vector_field = FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=1536)  # 根据您使用的模型调整维度
doc_id_field = FieldSchema(name="doc_id", dtype=DataType.INT64)
chunk_id_field = FieldSchema(name="chunk_id", dtype=DataType.INT64)

# 创建集合架构
schema = CollectionSchema(fields=[id_field, content_field, vector_field, doc_id_field, chunk_id_field])

# 创建集合
collection = Collection(name="document_embeddings", schema=schema)

# 创建索引
index_params = {
    "metric_type": "COSINE",
    "index_type": "HNSW",
    "params": {"M": 8, "efConstruction": 64}
}
collection.create_index(field_name="embedding", index_params=index_params)
```

## 流式输出实现示例

FastAPI支持流式响应，以下是一个简单的实现示例:

```python
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
import asyncio
import json

app = FastAPI()

@app.post("/api/chat/stream")
async def stream_chat(request: dict):
    async def generate():
        # 在实际应用中，这里会调用大语言模型API
        responses = ["你好", "，", "我是", "Yue", "智能", "助手", "。", "有什么", "可以", "帮助", "您", "的", "吗", "？"]
        
        for chunk in responses:
            yield f"data: {json.dumps({'text': chunk, 'type': 'content'})}\n\n"
            await asyncio.sleep(0.1)  # 模拟延迟
        
        yield f"data: {json.dumps({'type': 'done'})}\n\n"
    
    return StreamingResponse(generate(), media_type="text/event-stream")
```

祝您开发愉快！ 