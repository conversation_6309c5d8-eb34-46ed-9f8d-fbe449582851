#!/usr/bin/env python3
"""
最终验证数据库
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

from app.db.database import AsyncSessionLocal
from app.models.user import User
from app.models.product import Product
from app.models.order import Order
from app.core.config import settings
from sqlalchemy import select

async def verify_final():
    """最终验证数据库"""
    print("🎉 数据库初始化完成！")
    print("=" * 50)
    
    print(f"📍 数据库位置: {settings.DATABASE_URL}")
    print(f"📁 数据库文件: ./data/yue_platform.db")
    
    # 检查文件大小
    db_path = "./data/yue_platform.db"
    if os.path.exists(db_path):
        file_size = os.path.getsize(db_path)
        print(f"📊 文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
    
    print("\n📋 数据统计:")
    
    async with AsyncSessionLocal() as session:
        # 统计各类数据
        user_count = len((await session.execute(select(User))).scalars().all())
        product_count = len((await session.execute(select(Product))).scalars().all())
        order_count = len((await session.execute(select(Order))).scalars().all())
        
        print(f"👥 用户数量: {user_count}")
        print(f"📦 产品数量: {product_count}")
        print(f"🛒 订单数量: {order_count}")
        
        # 显示部分数据
        print("\n📝 示例数据:")
        users = (await session.execute(select(User).limit(2))).scalars().all()
        for user in users:
            print(f"   👤 {user.full_name} ({user.email})")
        
        products = (await session.execute(select(Product).limit(2))).scalars().all()
        for product in products:
            print(f"   📱 {product.name} - ¥{product.price}")
    
    print("\n🚀 下一步操作:")
    print("1. 启动API服务:")
    print("   cd backend")
    print("   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print("\n2. 查看API文档: http://localhost:8000/docs")
    print("\n3. 重新初始化数据库:")
    print("   python backend/app/db/init_db.py --force")

if __name__ == "__main__":
    asyncio.run(verify_final())
