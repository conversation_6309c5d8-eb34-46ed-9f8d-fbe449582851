#!/usr/bin/env python3
"""
验证数据库数据
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

from app.db.database import AsyncSessionLocal
from app.models.user import User
from app.models.product import Product
from app.models.order import Order
from sqlalchemy import select

async def verify_database():
    """验证数据库数据"""
    print("🔍 验证数据库数据...")
    print("=" * 50)
    
    async with AsyncSessionLocal() as session:
        # 验证用户数据
        result = await session.execute(select(User))
        users = result.scalars().all()
        print(f"👥 用户数量: {len(users)}")
        for user in users:
            print(f"   - {user.full_name} ({user.email}) - 登录次数: {user.login_count}")
        
        print()
        
        # 验证产品数据
        result = await session.execute(select(Product))
        products = result.scalars().all()
        print(f"📦 产品数量: {len(products)}")
        for product in products:
            print(f"   - {product.name} (SKU: {product.sku}) - ¥{product.price}")
        
        print()
        
        # 验证订单数据
        result = await session.execute(select(Order))
        orders = result.scalars().all()
        print(f"🛒 订单数量: {len(orders)}")
        for order in orders:
            print(f"   - 订单 {order.order_number} - {order.status} - ¥{order.total_amount}")
    
    print("\n✅ 数据库验证完成！")

if __name__ == "__main__":
    asyncio.run(verify_database())
