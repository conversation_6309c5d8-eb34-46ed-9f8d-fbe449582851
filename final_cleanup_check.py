#!/usr/bin/env python3
"""
最终清理检查
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

from app.core.config import settings

def final_cleanup_check():
    """最终清理检查"""
    print("🧹 数据库文件清理完成！")
    print("=" * 40)
    
    # 检查配置
    print(f"📋 系统配置: {settings.DATABASE_URL}")
    
    # 检查数据库文件
    db_path = "./data/yue_platform.db"
    if os.path.exists(db_path):
        file_size = os.path.getsize(db_path)
        print(f"✅ 正在使用: {db_path}")
        print(f"📊 文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
    else:
        print(f"❌ 数据库文件不存在: {db_path}")
    
    # 检查是否还有其他数据库文件
    print("\n🔍 检查其他数据库文件:")
    
    # 检查根目录
    root_db = "./yue_platform.db"
    if os.path.exists(root_db):
        print(f"⚠️ 发现: {root_db}")
    else:
        print(f"✅ 已清理: {root_db}")
    
    # 检查backend目录
    backend_files = []
    for root, dirs, files in os.walk("backend"):
        for file in files:
            if file.endswith(".db"):
                backend_files.append(os.path.join(root, file))
    
    if backend_files:
        print(f"⚠️ backend目录中发现数据库文件:")
        for f in backend_files:
            print(f"   - {f}")
    else:
        print(f"✅ backend目录已清理")
    
    print("\n🎉 清理总结:")
    print("✅ 保留: ./data/yue_platform.db (正在使用)")
    print("✅ 删除: 所有无用的数据库文件")
    print("✅ 系统配置正确")

if __name__ == "__main__":
    final_cleanup_check()
