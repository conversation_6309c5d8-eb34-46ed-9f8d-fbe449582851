#!/usr/bin/env python3
"""
检查当前使用的数据库文件
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

from app.core.config import settings

def check_database_files():
    """检查数据库文件状态"""
    print("🔍 检查数据库文件状态...")
    print("=" * 50)
    
    # 显示当前配置的数据库URL
    print(f"📋 当前配置的数据库URL: {settings.DATABASE_URL}")
    
    # 解析数据库路径
    if "sqlite" in settings.DATABASE_URL:
        # 提取SQLite文件路径
        db_path = settings.DATABASE_URL.split("///")[-1]
        print(f"📁 数据库文件路径: {db_path}")
        
        # 检查文件是否存在
        if os.path.exists(db_path):
            file_size = os.path.getsize(db_path)
            print(f"✅ 数据库文件存在")
            print(f"📊 文件大小: {file_size:,} 字节 ({file_size/1024:.1f} KB)")
        else:
            print(f"❌ 数据库文件不存在: {db_path}")
    
    print("\n🗂️ 项目中所有数据库文件:")
    
    # 检查所有可能的数据库文件位置
    possible_db_files = [
        "./yue_platform.db",
        "./data/yue_platform.db", 
        "./backend/yue_platform.db",
        "./backend/data/yue_platform.db"
    ]
    
    for db_file in possible_db_files:
        if os.path.exists(db_file):
            file_size = os.path.getsize(db_file)
            print(f"✅ {db_file} - {file_size:,} 字节 ({file_size/1024:.1f} KB)")
        else:
            print(f"❌ {db_file} - 不存在")

if __name__ == "__main__":
    check_database_files()
