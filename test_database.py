#!/usr/bin/env python3
"""
测试数据库连接和数据
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

from app.db.database import AsyncSessionLocal
from app.models.user import User
from app.models.product import Product
from app.models.order import Order
from sqlalchemy import select

async def test_database():
    """测试数据库连接和数据"""
    print("🔍 测试数据库连接和数据...")
    
    async with AsyncSessionLocal() as session:
        # 测试用户数据
        result = await session.execute(select(User))
        users = result.scalars().all()
        print(f"👥 用户数量: {len(users)}")
        for user in users[:3]:  # 显示前3个用户
            print(f"   - {user.full_name} ({user.email})")
        
        # 测试产品数据
        result = await session.execute(select(Product))
        products = result.scalars().all()
        print(f"📦 产品数量: {len(products)}")
        for product in products[:3]:  # 显示前3个产品
            print(f"   - {product.name} (¥{product.price})")
        
        # 测试订单数据
        result = await session.execute(select(Order))
        orders = result.scalars().all()
        print(f"🛒 订单数量: {len(orders)}")
        for order in orders[:3]:  # 显示前3个订单
            print(f"   - 订单 {order.order_number} ({order.status})")
    
    print("✅ 数据库测试完成！")

if __name__ == "__main__":
    asyncio.run(test_database())
