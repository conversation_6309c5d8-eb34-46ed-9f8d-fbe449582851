#!/usr/bin/env python3
"""
检查数据库配置
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

from app.core.config import settings

def check_db_config():
    """检查数据库配置"""
    print("🔍 数据库配置检查")
    print("=" * 40)
    
    print(f"📋 配置的数据库URL: {settings.DATABASE_URL}")
    
    # 解析路径
    if "sqlite" in settings.DATABASE_URL:
        db_path = settings.DATABASE_URL.split("///")[-1]
        print(f"📁 数据库文件路径: {db_path}")
        
        abs_path = os.path.abspath(db_path)
        print(f"📍 绝对路径: {abs_path}")
        
        if os.path.exists(abs_path):
            file_size = os.path.getsize(abs_path)
            print(f"✅ 文件存在，大小: {file_size:,} 字节")
        else:
            print(f"❌ 文件不存在")
    
    print("\n✅ 系统正在使用正确的数据库文件: ./data/yue_platform.db")

if __name__ == "__main__":
    check_db_config()
