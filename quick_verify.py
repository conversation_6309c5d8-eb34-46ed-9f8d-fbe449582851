#!/usr/bin/env python3
"""
快速验证数据库
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

from app.db.database import AsyncSessionLocal
from app.models.user import User
from app.models.product import Product
from app.models.order import Order
from sqlalchemy import select

async def quick_verify():
    """快速验证数据库"""
    print("✅ 数据库初始化成功！")
    print("=" * 40)
    
    async with AsyncSessionLocal() as session:
        # 统计数据
        user_count = len((await session.execute(select(User))).scalars().all())
        product_count = len((await session.execute(select(Product))).scalars().all())
        order_count = len((await session.execute(select(Order))).scalars().all())
        
        print(f"👥 用户数量: {user_count}")
        print(f"📦 产品数量: {product_count}")
        print(f"🛒 订单数量: {order_count}")
    
    print("\n🚀 现在可以启动API服务:")
    print("   cd backend")
    print("   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print("\n📖 API文档: http://localhost:8000/docs")

if __name__ == "__main__":
    asyncio.run(quick_verify())
