#!/usr/bin/env python3
"""
调试配置问题
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "backend"))

from app.core.config import settings

def debug_config():
    """调试配置"""
    print("🔍 调试数据库配置...")
    print("=" * 50)
    
    print(f"当前工作目录: {os.getcwd()}")
    print(f"数据库URL: {settings.DATABASE_URL}")
    
    # 解析数据库路径
    if "sqlite" in settings.DATABASE_URL:
        db_path = settings.DATABASE_URL.split("///")[-1]
        print(f"数据库文件路径: {db_path}")
        
        # 检查路径的各个部分
        abs_path = os.path.abspath(db_path)
        print(f"绝对路径: {abs_path}")
        
        # 检查目录是否存在
        dir_path = os.path.dirname(abs_path)
        print(f"目录路径: {dir_path}")
        print(f"目录是否存在: {os.path.exists(dir_path)}")
        
        # 检查文件是否存在
        print(f"文件是否存在: {os.path.exists(abs_path)}")
        
        # 尝试创建目录
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"✅ 创建目录: {dir_path}")
            except Exception as e:
                print(f"❌ 创建目录失败: {e}")
        
        # 检查权限
        try:
            # 尝试创建一个测试文件
            test_file = os.path.join(dir_path, "test_write.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            print("✅ 目录写权限正常")
        except Exception as e:
            print(f"❌ 目录写权限问题: {e}")

if __name__ == "__main__":
    debug_config()
